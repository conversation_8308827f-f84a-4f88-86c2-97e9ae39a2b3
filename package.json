{"name": "abun-react-frontend", "version": "0.1.0", "private": true, "dependencies": {"@creativebulma/bulma-divider": "^1.1.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/inter": "^5.2.5", "@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.6", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hotjar/browser": "^1.0.9", "@lottiefiles/react-lottie-player": "^3.5.3", "@mui/material": "^5.15.20", "@mui/x-date-pickers": "^7.9.0", "@popperjs/core": "^2.11.8", "@stripe/react-stripe-js": "^2.1.2", "@stripe/stripe-js": "^2.1.0", "@tanstack/react-query": "^4.29.17", "@tanstack/react-table": "^8.9.3", "@tawk.to/tawk-messenger-react": "^2.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tippyjs/react": "^4.2.6", "@types/jest": "^27.5.2", "@types/node": "^16.18.18", "@types/papaparse": "^5.3.7", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-lottie": "^1.2.10", "@types/react-router-dom": "^5.3.3", "@types/url-parse": "^1.4.8", "abun-react-frontend": "file:", "aieditor": "^1.1.0", "axios": "^1.3.4", "bulma": "^0.9.4", "bulma-switch": "^2.0.4", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "easymde": "2.16.0", "extract-domain": "^4.1.4", "grecaptcha": "^1.0.3", "html2pdf.js": "^0.10.2", "jspdf": "^3.0.1", "ldrs": "^1.0.2", "lucide-react": "^0.483.0", "moment-timezone": "^0.5.43", "papaparse": "^5.4.1", "posthog-js": "^1.154.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-google-recaptcha": "^3.1.0", "react-helmet": "^6.1.0", "react-icons-kit": "^2.0.0", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-popper": "^2.3.0", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-simplemde-editor": "^5.2.0", "react-timezone-select": "^2.1.2", "react-tooltip": "^5.21.1", "react-window": "^1.8.10", "recharts": "^2.15.1", "remove-markdown": "^0.5.5", "sass": "^1.89.0", "tippy.js": "^6.3.7", "typescript": "^4.9.5", "url-parse": "^1.5.10", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "minify-all-scss": "node optimize-all-scss.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/grecaptcha": "^3.0.9", "autoprefixer": "^10.4.21", "cssnano": "^7.0.6", "glob": "^11.0.1", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "sharp": "^0.34.1"}}