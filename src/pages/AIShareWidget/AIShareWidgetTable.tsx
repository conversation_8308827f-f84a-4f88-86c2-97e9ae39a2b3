import { useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { useNavigate } from "react-router-dom";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import LinkButton from "../../components/LinkButton/LinkButton";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { AIShareWidget, AIShareWidgetListApiResponse, getAIShareWidgetsQuery } from "../../utils/api";
import './AIShareWidget.min.css';

function AIShareWidgetTable() {
    // ---------------------- <PERSON><PERSON> STATE CONSTANTS ----------------------
    const pageSizes = [15, 25, 50, 100];

    // -------------------------- STATES --------------------------
    const [widgets, setWidgets] = useState<AIShareWidget[]>([]);
    const navigate = useNavigate();

    // -------------------------- QUERIES --------------------------
    const {
        isLoading,
        isError,
        data,
        refetch
    } = useQuery<AIShareWidgetListApiResponse>({
        ...getAIShareWidgetsQuery()
    });

    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        console.log('Raw data received:', data); // Debug log
        
        if (data) {
            let widgetsData: AIShareWidget[] = [];
            
            if (data.success && data.widgets) {
                // Proper response structure
                widgetsData = data.widgets;
            } else if (Array.isArray(data)) {
                // If data is directly an array (fallback)
                widgetsData = data as AIShareWidget[];
            } else if ((data as any).data && (data as any).data.widgets) {
                // If data is wrapped in another data property (fallback)
                widgetsData = (data as any).data.widgets;
            } else {
                console.error('Unexpected data structure:', data);
                widgetsData = [];
            }
            
            console.log('Processed widgets data:', widgetsData); // Debug log
            setWidgets(widgetsData); 
        }
    }, [data]);

    // Handle errors with useEffect
    useEffect(() => {
        if (isError) {
            console.error('Error loading widgets');
            errorAlertRef.current?.show("Failed to load widgets");
        }
    }, [isError]);

    // ---------------------- TABLE COLUMN DEFS ----------------------
    const columnHelper = createColumnHelper<AIShareWidget>();
    const columnDefs: ColumnDef<AIShareWidget, any>[] = [
        columnHelper.accessor('name', {
            id: 'name',
            header: "Widget Name",
            cell: (props) => {
                const handleClick = () => {
                    navigate(`/ai-share-widget/view/${props.row.original.widget_id}/`);
                };

                return (
                    <span onClick={handleClick} style={{ cursor: 'pointer' }}>
                        {props.row.original.name}
                    </span>
                );
            },
            enableGlobalFilter: true
        }),
        columnHelper.accessor('created_at', {
            id: 'date',
            header: "Created On",
            cell: cellProps => {
                const selectedDate = cellProps.row.original.created_at;

                // defining date showing context
                const getRelativeTime = (dateString: string) => {
                    const createdDateObj = new Date(dateString);
                    const now = new Date();
                    const timeDiff = now.getTime() - createdDateObj.getTime();

                    // Handle future dates
                    if (timeDiff < 0) {
                        return "just now";
                    }

                    const seconds = Math.floor(timeDiff / 1000);
                    const minutes = Math.floor(seconds / 60);
                    const hours = Math.floor(minutes / 60);
                    const days = Math.floor(hours / 24);

                    // Check conditions in ascending order of time units
                    if (seconds < 60) {
                        return "just now";
                    }

                    if (minutes < 60) {
                        return minutes === 1 ? "a minute ago" : `${minutes} minutes ago`;
                    }

                    if (hours < 24) {
                        return hours === 1 ? "an hour ago" : `${hours} hours ago`;
                    }

                    if (days > 30) {
                        const day = createdDateObj.getDate();
                        const month = createdDateObj.toLocaleString('default', { month: 'short' });
                        const year = createdDateObj.getFullYear().toString().slice(-2);
                        return `${day} ${month}, ${year}`;
                    }

                    return days === 1 ? "a day ago" : `${days} days ago`;
                };

                return getRelativeTime(selectedDate);
            },
            meta: {
                align: 'center'
            }
        }),
        columnHelper.display({
            id: 'view',
            header: () => "View",
            cell: cellProps => {
                const isActive: boolean = cellProps.row.original.is_active;
                
                return (
                    <LinkButton
                        linkTo={`/ai-share-widget/view/${cellProps.row.original.widget_id}/`}
                        text={"View"}
                        type={"success"}
                        width={"100px"}
                        additionalClassList={["is-small", "more-rounded-borders"]}
                        disabled={!isActive}
                    />
                );
            },
            enableGlobalFilter: false,
            meta: {
                align: 'center'
            }
        }),
    ];

    function goBack() {
        navigate(-1);
    }

    if (isLoading) {
        return (
            <div className={"w-100 is-flex is-justify-content-center is-align-items-center"}>
                <AbunLoader show={isLoading} height="20vh" />
            </div>
        );
    }

    // Add error handling
    if (isError) {
        return (
            <div className="w-100 is-flex is-justify-content-center is-align-items-center">
                <div className="has-text-danger">
                    Error loading widgets. Please try again.
                </div>
            </div>
        );
    }

    return (
        <>
            <div className="ai-share-widget-table w-100">
                <Helmet>
                    <title>AI Share Widget Projects | Abun.com</title>
                    <meta
                        name="description"
                        content="Manage and track your AI share widget projects."
                    />
                </Helmet>
                <div className={""}>
                    
                    <div className="ai-widget-abun-table">
                        <AbunTable 
                            tableContentName={"AI Share Widgets"}
                            tableData={widgets}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[0]}
                            noDataText={"No Widget data available."}
                            searchboxPlaceholderText={"Search widgets..."}
                        />
                    </div>
                    <SuccessAlert ref={successAlertRef} />
                    <ErrorAlert ref={errorAlertRef} />
                </div>
            </div>
        </>
    );
}

export default AIShareWidgetTable;