    @import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";
@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";
@import "../../assets/bulma-overrides/bulmaOverrides";

.statistic-page-generator-container {
  font-family: $primary-font !important;
  background-color: #ffffff;
  

&.full-stats-view {
  margin: 0;
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh; 
  overflow: hidden; 
  z-index: 99;
  background-color: #fff;
}


.statistic-page-generator-header {
  position: relative;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &.stats-view {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: #fff;
    border-bottom: none;
    box-shadow: none;
    height: 6rem;
    padding: 2rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }


  .left-header-section {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap; 
    margin-left: 0;
    flex: 1;
    min-width: 0; 

    .back-btn {
      cursor: pointer;
      margin-right: 1rem;
      flex-shrink: 0; 
      
      svg {
        width: 30px;
        height: 24px;
      }
    }

    .abun-logo {
      margin: 0 1.25rem;
      width: 52px;
      height: 48px;
      flex-shrink: 0; 
    }

    .Tabs {
      margin: 0 0 0 2.75rem;
      flex-shrink: 0; 

      .Tab {
        &.active {
          font-size: 1.25rem;
          font-weight: 600;
          color: #3F77F8;
          border-bottom: 3px solid #3F77F8;
          opacity: 1;
          white-space: nowrap; 
        }
      }
    }
  }

  .right-header-section {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: nowrap;
    gap: 0.75rem; 
    flex-shrink: 0;
    min-width: fit-content;

    .editor-controls {
      display: flex;
      gap: 0.75rem; 
      align-items: center;
      white-space: nowrap;

      .sidebar-button.save-btn {
        padding: 0.625rem 1.25rem; 
        border: 1px solid #3F77F8;
        border-radius: 0.25rem;
        background-color: #3F77F8;
        color: #fff;
        cursor: pointer;
        font-size: 0.875rem; 
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: nowrap;
        flex-shrink: 0; 

        &:hover:not(:disabled) {
          background-color: #2563eb;
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          background-color: #6c757d;
        }
      }

      svg.collapse-button {
        cursor: pointer;
        flex-shrink: 0; 
        width: 20px;
        height: 20px;

        &:hover path {
          fill: #3F77F8 !important;
          fill-opacity: 0.8;
        }

        &.collapsed {
          background: linear-gradient(to left, #000 29%, white 25%) !important;
        }
      }
    }
  }

  @media (max-width: 1024px) {
    &.stats-view {
      padding: 1.5rem 1rem; 
    }

    .left-header-section {
      .abun-logo {
        margin: 0 0.75rem; 
        width: 42px; 
        height: 38px;
      }

      .Tabs {
        margin: 0 0 0 1.5rem;

        .Tab.active {
          font-size: 1.125rem; 
        }
      }
    }

    .right-header-section {
      gap: 0.5rem; 

      .editor-controls {
        gap: 0.5rem;

        .sidebar-button.save-btn {
          padding: 0.5rem 1rem;
          font-size: 0.8rem;
        }
      }
    }
  }

  @media (max-width: 768px) {
    &.stats-view {
      height: auto;
      min-height: 60px;
      padding: 1rem 0.75rem;
    }

    .left-header-section {
      .back-btn {
        margin-right: 0.5rem;
        
        svg {
          width: 24px; 
          height: 20px;
        }
      }

      .abun-logo {
        margin: 0 0.5rem;
        width: 36px;
        height: 32px;
      }

      .Tabs {
        margin: 0 0 0 1rem;

        .Tab.active {
          font-size: 1rem;
        }
      }
    }

    .right-header-section {
      gap: 0.375rem; 

      .editor-controls {
        gap: 0.375rem;

        .sidebar-button.save-btn {
          padding: 0.5rem 0.75rem;
          font-size: 0.75rem;
        }

        svg.collapse-button {
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    &.stats-view {
      padding: 0.75rem 0.5rem;
    }

    .left-header-section {
      .back-btn {
        margin-right: 0.25rem;
      }

      .abun-logo {
        margin: 0 0.25rem;
        width: 32px;
        height: 28px;
      }

      .Tabs {
        margin: 0 0 0 0.5rem;

        .Tab.active {
          font-size: 0.9rem;
        }
      }
    }

    .right-header-section {
      .editor-controls {
        .sidebar-button.save-btn {
          padding: 0.4rem 0.6rem;
          font-size: 0.7rem;
        }

        svg.collapse-button {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.statistic-page-generator-content {
    &.stats-view {
            padding: 0;
    height: calc(100vh - 6rem);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    }
    
    &.generator-view {
        padding: 0;
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 100%; 
        box-sizing: border-box;
    }

    
    .statistic-generator-header{
        h2 {
            font-size: 2rem !important;
            font-weight: 600 !important;
            margin-bottom: 4px;
        }
    
        p {
            color: #969696!important;
            font-family: $secondary-font !important;
            font-size: 1.125rem !important;
        }
    }

} 

.ca-content-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    gap: 40px;
    box-sizing: border-box;

    @media(max-width: 1500px) {
        flex-direction: column;
        gap: 20px;
        max-width: 100%;
    }
    
    @media(max-width: 768px) {
        gap: 15px;
        padding: 0 10px;
    }
}

.ca-form-column {
    display: flex;
    flex-direction: column;
    flex-basis: 40%;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-start;
    width: 100%; 
    box-sizing: border-box;

    @media(max-width: 1400px) {
        flex-basis: 100%;
        max-width: 100%;
    }
    
    @media(max-width: 768px) {
        margin-top: 10px;
        gap: 8px;
    }
}

.ca-label {
    font-size: 18px;
    color: #333;
    font-weight: 600;
    
    @media(max-width: 480px) {
        font-size: 16px;
    }
}

.ca-input,
.ca-textarea {
    padding: 10px;
    font-size: 14px;
    border-radius: 10px;
    background-color: #fff;
    border: 1px solid #b0b0b0;
    margin-top: -6px;
    box-sizing: border-box;
    min-width: 400px;
    width: 100%; 
    max-width: 100%;
    margin-bottom: 10px;
    font-family: 'Inter';
    
    @media(max-width: 768px) {
        padding: 12px 10px;
        font-size: 16px;
        min-width: unset;
    }
    
    @media(max-width: 480px) {
        padding: 12px 8px;
        font-size: 16px;
        border-radius: 8px;
        margin-top: 15px;
    }
}

.ca-input::placeholder {
    @media(max-width:480px) {
        font-size: 14px;
    }
}

.ca-input:focus {
    border-color: #2684FF;
    box-shadow: 0 0 0 1px #2684FF;
    outline: none !important;
    transition: all 100ms;
}

.ca-button {
    background-color: rgba(63, 119, 248, 1);
    color: white;
    border: none;
    padding: 10px 15px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;
    align-self: flex-start;
    
    @media(max-width: 480px) {
        width: 100%;
        align-self: stretch;
        padding: 12px 15px;
    }
}

.ca-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.ca-button:hover:not(:disabled) {
    background-color: #2c5aa0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(63, 119, 248, 0.2);
}

.ca-suggestion-box {
    flex-basis: 60%;
    background: rgba(143, 191, 250, 0.15);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ccc;
    height: auto;
    min-height: 580px;
    min-width: 640px;
    width: 100%; 
    max-width: 100%; 
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    @media(max-width: 1400px) {
        flex-basis: 100%;
        position: relative;
        bottom: 50px;
        height: auto;
        min-height: 320px;
        max-width: 100%; 
        min-width: unset; 
        width: 100%;
    }
    
    @media(max-width: 768px) {
        padding: 15px;
        bottom: 30px;
        min-height: 280px;
        margin: 0; 
    }
    
    @media(max-width: 480px) {
        padding: 12px;
        bottom: 20px;
        min-height: 250px;
        border-radius: 6px;
    }
}

.ca-suggestion-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 100%;
    box-sizing: border-box;
}

.ca-empty-state {
    align-items: center;
    text-align: center;
    width: 100%;
}

.ca-empty-state-title {
    color: #333;
    font-weight: 500;
    margin-top: 50px;
    font-family: Inter;
    font-style: italic;
    font-size: 18px;
    text-align: center;
    margin-bottom: 0;
    
    @media(max-width: 768px) {
        margin-top: 30px;
        font-size: 16px;
    }
    
    @media(max-width: 480px) {
        margin-top: 20px;
        font-size: 14px;
    }
}

.ca-loading-state {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-top: 20px;
    text-align: left;
    width: 100%;
    
    @media(max-width: 480px) {
        padding-top: 15px;
    }
}

.ca-loading-text {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    font-family: Inter;
    
    @media(max-width: 480px) {
        font-size: 13px;
    }
}

.ca-suggestion-title {
    font-size: 20px;
    margin-bottom: 40px;
    color: #333;
    text-align: center;
    font-weight: bold;
    font-family: Bricolage Grotesque;
    
    
    @media(max-width: 768px) {
        font-size: 18px;
        margin-bottom: 30px;
    }
    
    @media(max-width: 480px) {
        font-size: 16px;
        margin-bottom: 20px;
    }
}

.ca-suggestion-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}

.ca-suggestion-item {
    font-family: Inter;
    margin-bottom: 5px;
    font-size: 14px;
    color: #333;
    font-weight: 600;
    margin-bottom: 20px;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word; 
    
    @media(max-width: 768px) {
        padding: 12px 14px;
        margin-bottom: 15px;
        font-size: 13px;
    }
    
    @media(max-width: 480px) {
        padding: 10px 12px;
        margin-bottom: 12px;
        font-size: 12px;
    }
}

.ca-suggestion-item:hover {
    text-decoration: underline;
}

.Generate_button_large {
    display: block;
    margin-top: 10px;
    width: 100%;

    @media(max-width:1500px) {
        display: none;
        opacity: 0;
    }
}

.Generate_button {
    display: none;

    @media(max-width:1500px) {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 50px;
        bottom: 35px;
        right: 5px;
        width: 100%;
    }

    @media(max-width:988px) {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        bottom: 35px;
        right: 5px;
        width: 100%;
    }
    
    @media(max-width: 480px) {
        margin-top: 30px;
        bottom: 20px;
        right: 0;
    }
}

#tooltip {
    transform: translateX(-50%);
    left: 50%;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

#style_button {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}

#buttonTip {
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}

@media (min-width: 1500px) {
    .ca-content-row {
        flex-direction: row;
    }

    .ca-form-column {
        flex-basis: 40%;
    }

    .ca-suggestion-box {
        flex-basis: 60%;
    }
}

@media (max-width: 320px) {
    .ca-input,
    .ca-textarea {
        padding: 10px 6px;
        font-size: 14px;
    }
    
    .ca-suggestion-box {
        padding: 10px;
    }
    
    .ca-suggestion-item {
        padding: 8px 10px;
        font-size: 11px;
    }
}

.stats-result-section-new {
  display: flex;
  gap: 0;
  height: 100%;
  flex: 1;
  overflow: hidden;

  .stats-preview-main {
      flex: 1;
  min-width: 0;
  background-color: #fff;
  border-radius: 0;
  border: none;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

    .ai-preview-container {
        padding: 16px;
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;


      .ai-preview-content {
width: 100%;
min-height: 100%;

&:focus {
  outline: none !important;
  border: none !important;
}

&:focus-visible {
  outline: none !important;
  border: none !important;
}

&:focus {
  box-shadow: none !important;
}
}
    }
  }


   .iframe-container {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background-color: white;
  width: 100%;
  height: 100%; 
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  flex: 1; 
}

.stats-iframe,
iframe {
  width: 100%;
  height: 100%; 
  border: none;
  background-color: white;
  display: block;
  flex: 1;
}

  .stats-sidebar {
    width: 380px;
      flex-shrink: 0;
      background-color: #fff;
      border-radius: 0;
      border: none;
      border-left: 1px solid #e5e7eb;
      height: 100%;
      overflow-y: auto;
      margin-top: 0;

    .sidebar-section {
      border-bottom: 1px solid #e5e7eb;

.sidebar-dropdown-header {
padding: 16px 20px 0px 20px;
cursor: pointer;
background-color: #fff;
font-weight: 400;
font-size: 15px;
color: #4b5563;
display: flex;
align-items: center;
justify-content: flex-start;
transition: all 0.2s ease;
border: none;
margin-bottom: 16px; 

h6 {
  font-size: 1.125rem;
  font-weight: 400;
  color: #3F77F8;
  width: 100%;
  font-family: Inter;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
}

&::before {
  content: "";
  width: 12px;
  height: 7px;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='7' viewBox='0 0 12 7' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 5.25L6.5 0L13 5.25L12 6.5L6.5 2.5L1 6.5L0 5.25Z' fill='%236B7280' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

&.active {
  margin-bottom: 0; 
  
  &::before {
    transform: rotate(180deg);
  }
}

&:hover {
  background-color: #f9fafb;
}

&.version-header {
  justify-content: flex-start;
  gap: 12px;

  .version-count {
    background-color: #3b82f6;
    color: #fff;
    padding: 3px 10px;
    border-radius: 14px;
    font-size: 13px;
    min-width: 22px;
    text-align: center;
    font-weight: 500;
    margin-left: auto;
  }
}

span {
  font-size: 15px;
  color: #4b5563;
  font-weight: 400;
  line-height: 1.4;
}
}
      .sidebar-dropdown-content {
        display: none;
        padding: 20px 24px;
        background-color: #fff;

        .sidebar-textarea {
          width: 100%;
          min-height: 100px;
          padding: 14px 16px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          resize: vertical;
          margin-bottom: 20px;
          font-size: 14px;
          font-family: inherit;
          box-sizing: border-box;
          color: #374151;
          line-height: 1.5;

          &:focus {
  border-color: #3F77F8;
}

          &.embed-code {
            background-color: #f9fafb;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            min-height: 80px;
            margin-bottom: 5px;
          }

          &::placeholder {
            color: #9ca3af;
          }
        }


.sidebar-button {
  width: 100%;
  padding: 12px 18px;
  color: #fff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;

  &.update-btn {         
      background-color: #3b82f6;          

      &:hover:not(:disabled) {             
          background-color: #2563eb;         
      }          

      &:disabled {             
          opacity: 0.6;             
          cursor: not-allowed;         
      }     
  }      

  .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
  }

  .spinner {         
      width: 12px;         
      height: 12px;         
      border: 2px solid #ffffff;         
      border-top: 2px solid transparent;         
      border-radius: 50%;         
      animation: spin 1s linear infinite;
      flex-shrink: 0;
  }      

  @keyframes spin {         
      0% { transform: rotate(0deg); }         
      100% { transform: rotate(360deg); }     
  } 


  &.copy-btn {
      background-color: #007bff;
      width: auto; 
      padding: 6px 12px;
      font-size: 12px; 
      margin-left: auto;
      margin-top:0px; 
      margin-bottom:0px; 
      display: block; 

      &:hover {
          background-color: #2a7ad0;
      }
  }

  &.small {
      width: auto;
      padding: 6px 10px;
      font-size: 9px;

      &.switch-btn {
          background-color: #3b82f6;

          &:hover {
              background-color: #2563eb;
          }
      }

      &.danger {
          background-color: #ef4444;

          &:hover {
              background-color: #dc2626;
          }
      }
  }
}

        .versions-list-sidebar {
          max-height: 400px;
          overflow-y: auto;

          .version-item-sidebar {
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 12px;
            background-color: #fff;
            transition: all 0.2s ease;

            &:hover {
              border-color: #d1d5db;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            &.current-version {
              background-color: #eff6ff;
              border-color: #3b82f6;
            }

            .version-header-sidebar {
display: flex;
justify-content: space-between;
align-items: flex-start;
width: 100%;

.version-info-sidebar {
  flex: 1;
  min-width: 0;
}

.version-actions-sidebar {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: auto;
  align-items: flex-start;
  min-width: fit-content; 
  justify-content: flex-end; 
  width: auto;
}
}

  .version-description-sidebar {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.5;
  }
}

    .empty-versions {
      text-align: center;
      color: #6b7280;
      font-style: italic;
      font-size: 14px;
      margin: 0;
      padding: 24px;
    }
  }

  .embed-description {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 20px;
    margin: 0 0 20px 0;
    line-height: 1.5;
  }

  .stats-id {
    font-size: 13px;
    color: #6b7280;
    text-align: center;
    margin: 0;
  }
}

      &:has(.sidebar-dropdown-header.active) .sidebar-dropdown-content {
        display: block;
      }
    }
  }
}

@media screen and (min-resolution: 1.5dppx) {
  .ai-stats-page-container.full-stats-view {
    height: 100vh;
  }
  
  .ai-stats-page-content.stats-view {
    height: calc(100vh - 6rem);
  }
}

@media screen and (min-resolution: 2dppx) {
  .ai-stats-page-container.full-stats-view {
    height: 100vh;
  }
  
  .ai-stats-page-content.stats-view {
    height: calc(100vh - 6rem);
  }
}

@media (max-width: 768px) {
  .ai-stats-page-content.stats-view {
    height: calc(100vh - 5rem); 
  }
}

@media (max-width: 480px) {
  .ai-stats-page-content.stats-view {
    height: calc(100vh - 4rem); 
  }
}
  
.sidebar-button {
  &.save-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid #3F77F8;
    border-radius: 0.25rem;
    background-color: #3F77F8;
    color: #fff;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover:not(:disabled) {
      background-color: #2563eb;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background-color: #6c757d;
    }
  }
}


svg.collapse-button {
  cursor: pointer;
  margin-right: 5px;

  &:hover path {
    fill: #3F77F8 !important;
    fill-opacity: 0.8;
  }

  &.collapsed {
    background: linear-gradient(to left, #000 29%, white 25%) !important;
  }
}


.stats-sidebar {
  transition: all 0.3s ease;
  
  &.collapsed {
    width: 0;
    overflow: hidden;
    border-left: none;
  }
}

// Mobile responsive styles
@media (max-width: 768px) {
  .right-header-section {
    flex-wrap: nowrap;
    gap: 0.5rem;
    
    .editor-controls {
      gap: 0.5rem;
      
      .save-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
        white-space: nowrap;
      }
    }
  }
  
  .stats-sidebar {
    &:not(.collapsed) {
      position: fixed;
      top: 60px; 
      left: 0;
      width: 100vw;
      height: calc(100vh - 60px);
      z-index: 1001;
      background-color: white;
    }
  }
}

.modal, .modal-background {
  z-index: 1050 !important;
}

.modal-card, .modal-content {
  z-index: 1051 !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa;
  padding: 40px;
  flex: 1;
}

}
