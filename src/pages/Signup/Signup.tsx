import { faArrowRight, faStar } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { Icon } from 'react-icons-kit';
import { eye } from 'react-icons-kit/feather/eye';
import { eyeOff } from 'react-icons-kit/feather/eyeOff';
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import LocalIcon from "../../components/Icon/Icon";
import { storeAccessToken, storeRefreshToken } from "../../utils/jwt";
import { pageURL } from "../routes";
import './Signup.min.css';

export default function Signup() {
	// ------------------------- QUERY PARAMETERS -----------------------
	const [searchParams] = useSearchParams();
	const appSumoCode = searchParams.get("code");
	const planName = searchParams.get("plan");

	// ----------------------- STATES -----------------------
	const [signupUnderway, setSignupUnderway] = useState(false);
	const [googleSignupUnderWay, setGoogleSignupUnderWay] = useState(false);

	// ----------------------- REFS -----------------------
	// TODO: need to replace this sometime later
	const usernameElementRef = useRef<HTMLInputElement>(null);
	const emailElementRef = useRef<HTMLInputElement>(null);
	const passwordElementRef = useRef<HTMLInputElement>(null);
	const submitButtonRef = useRef<HTMLButtonElement>(null);
	const navigate = useNavigate();


	useEffect(() => {
		document.title = "Signup | Abun"
	}, []);

	/* -------------- validation functions -------------- */
	function usernameIsValid(value: string): boolean {
		return /^[a-zA-Z0-9\s]+$/.test(value) && (value.length <= 35);
	}

	function emailIsValid(value: string): boolean {
		return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value);
	}

	function passwordIsValid(value: string): boolean {
		return value.length >= 6
	}

	/* ------------------------------------------------- */

	/**
	 * Resets all previous highlights and messages when called.
	 */
	function clearAllInputErrorHighlights() {
		if (usernameElementRef.current && emailElementRef.current && passwordElementRef.current) {
			usernameElementRef.current.classList.remove('is-danger');
			emailElementRef.current.classList.remove('is-danger');
			passwordElementRef.current.classList.remove('is-danger');
			if (usernameElementRef.current.parentElement) {
				usernameElementRef.current.parentElement.dataset.errMsg = "";
			}
			if (emailElementRef.current.parentElement) {
				emailElementRef.current.parentElement.dataset.errMsg = "";
			}
			if (passwordElementRef.current.parentElement) {
				passwordElementRef.current.parentElement.dataset.errMsg = "";
			}
		} else {
			console.error("clearAllInputErrorHighlights() - input element reference is missing");
		}
	}

	/**
	 * Higlights input and shows error message below them. Resets all previous highlights and messages when called.
	 * @param input: key representing the input field that needs to be marked
	 * @param errorMsg: message to show below the input field
	 */
	function markBadInput(input: "username" | "email" | "password", errorMsg: string) {
		if (usernameElementRef.current && emailElementRef.current && passwordElementRef.current) {
			// reset all previous error messages and border highlights
			clearAllInputErrorHighlights();

			switch (input) {
				case "username":
					usernameElementRef.current.classList.add('is-danger');
					if (usernameElementRef.current.parentElement) {
						usernameElementRef.current.parentElement.dataset.errMsg = errorMsg;
					}
					break;
				case "email":
					emailElementRef.current.classList.add('is-danger');
					if (emailElementRef.current.parentElement) {
						emailElementRef.current.parentElement.dataset.errMsg = errorMsg;
					}
					break;
				case "password":
					passwordElementRef.current.classList.add('is-danger');
					if (passwordElementRef.current.parentElement) {
						passwordElementRef.current.parentElement.dataset.errMsg = errorMsg;
					}
					break;
				default:
					console.error("bad 'input' argument value");
					break;
			}
		} else {
			console.error("markBadInput() - input element reference is missing");
		}
	}

	function setServerErrorMessage(err_id = 'server_error') {
		if (usernameElementRef.current) {
			if (usernameElementRef.current.parentElement) {
				if (err_id === 'DOMAIN_BLOCKED') {

					usernameElementRef.current.parentElement.dataset.errMsg = "We do not accept temporary/disposable emails.";
				}
				else if (err_id === 'ACCOUNT_ALREADY_EXISTS') {

					usernameElementRef.current.parentElement.dataset.errMsg = "Account already exists. Please login to continue.";
				}
			}

		} else if (submitButtonRef.current) {
			submitButtonRef.current.dataset.errMsg = "We are experiencing some issues. Please try again later.";

		}
	}

	function clearServerErrorMessage() {
		if (submitButtonRef.current) {
			submitButtonRef.current.dataset.errMsg = "";
		}
	}

	function submitSignupData() {
		clearServerErrorMessage();
		setSignupUnderway(true);

		const username: string = usernameElementRef.current?.value || "";
		const email: string = emailElementRef.current?.value || "";
		const password: string = passwordElementRef.current?.value || "";
		const timezone: string = Intl.DateTimeFormat().resolvedOptions().timeZone;

		/* check the input values */
		if (!usernameIsValid(username)) {
			markBadInput(
				"username",
				"Full Name should only contain alphanumeric characters & spaces"
			);
			setSignupUnderway(false);
			return;
		}

		if (!emailIsValid(email)) {
			markBadInput("email", "please enter a valid email address");
			setSignupUnderway(false);
			return;
		}

		if (!passwordIsValid(password)) {
			markBadInput("password", "password should have minimum of 6 characters");
			setSignupUnderway(false);
			return;
		}

		// let endpoint = "https://pro.ip-api.com/json/?fields=status,country&key=GN0GifpuBrQppdE";
		let endpoint = "https://api.ipdata.co/?fields=country_name&api-key=4346a4c48105f4c24084f18b6badc1ed363ab8bdd96420a2b070c10f";
		let xhr = new XMLHttpRequest();
		xhr.onreadystatechange = function () {
			if (this.readyState === 4 && this.status === 200) {
				let response = JSON.parse(this.responseText);

				/* submit data to server */
				clearAllInputErrorHighlights();

				// Prepare signup data
				const signupData: any = {
					username: username,
					email: email,
					password: password,
					country: response.country_name,
					timezone: timezone,
					appsumo_code: appSumoCode
				};

				// Add plan-related data if plan is selected
				if (planName) {
					const successURL = process.env.REACT_APP_STRIPE_CHECKOUT_SUCCESS_URL;
					const cancelURL = process.env.REACT_APP_FRONTEND_DOMAIN + pageURL['manageSubscription'];

					signupData.plan_name = planName;
					signupData.success_url = successURL;
					signupData.cancel_url = cancelURL;
				}

				axios({
					method: "post",
					url: process.env.REACT_APP_DRF_DOMAIN + "/api/frontend/signup/",
					responseType: 'json',
					data: signupData
				}).then(response => {
					if (response.status === 200) {
						// Account created successfully. Store tokens.
						let result = response.data;
						let accessToken: string = result['access_token'];
						let refreshToken: string = result['refresh_token'];

						storeAccessToken(accessToken);
						storeRefreshToken(refreshToken);

						// Check if plan was selected and stripe checkout URL is provided
						if (planName && result['stripe_checkout_url']) {
							// Redirect to stripe checkout
							window.location.href = result['stripe_checkout_url'];
						} else if (!appSumoCode) {
							// Normal flow - redirect to plan selection
							return navigate(pageURL['signupPlanSelection']);
						} else {
							// AppSumo flow - redirect to create article
							return navigate(pageURL['createArticle']);
						}

					} else {
						setSignupUnderway(false);
						setServerErrorMessage();
					}
				}).catch(err => {
					console.error(err);
					setServerErrorMessage(err.response.data.err_id);
					setSignupUnderway(false);
				});
			}
		};
		xhr.open('GET', endpoint, true);
		xhr.send();
	}

	const [password, setPassword] = useState("");
	const [type, setType] = useState('password');
	const [icon, setIcon] = useState(eyeOff);
	const handleToggle = () => {
		if (type === 'password') {
			setIcon(eye);
			setType('text')
		} else {
			setIcon(eyeOff)
			setType('password')
		}
	}
	const leftFeatures = [
		'25+ Tools',
		'Feature Rich Tools',
		'Great Support',
		'Frequent Feature Drops',
		'No Learning Curve',
		'Community Driven',
		'All in one Pricing',
	];

	const rightFeatures = [
		'Built for Scale',
		'Zero Setup Time',
		'Transparent Pricing',
		'Designed for Efficiency',
		'Enterprise-Grade Security',
		'Growing Integrations',
		'Privacy First',
	];

	const stars = (
		<div className="stars is-flex is-justify-content-center" style={{ marginBottom: '0.4rem', gap: '5px' }}>
			{[...Array(5)].map((_, i) => (
				<FontAwesomeIcon key={i} icon={faStar} style={{ fontSize: '1rem', color: '#FFD43B' }} />
			))}
		</div>
	);

	return (
		<div className="signup-container">

			<div className="card is-flex signup-card mt-5">
				<div className="card-content left-container">
					<h1 className="heading-text mb-5 epilogue">Signup for Abun.com</h1>

					<div className="google-separator">
						<button
							className="button is-rounded google-button is-flex is-align-items-center mb-5"
							disabled={signupUnderway || googleSignupUnderWay}
							onClick={() => {
								setGoogleSignupUnderWay(true);
								clearServerErrorMessage();

								axios({
									method: "get",
									url: process.env.REACT_APP_DRF_DOMAIN + "/api/fontend/google-signup-login-auth/?signup=true",
									responseType: 'json',
								}).then((response) => {
									if (response.status === 200) {
										const auth_url = response.data.authorization_endpoint;
										window.location.href = auth_url;
									} else {
										setGoogleSignupUnderWay(false);
										setServerErrorMessage();
									}
								})

							}}>
							<span>
								<LocalIcon iconName='google' width="1.7em" style={{ maxWidth: '1.7em' }} />
							</span>
							<span className="has-text-black ml-2 has-text-weight-normal is-size-5" style={{ marginRight: '3rem' }}>Continue with Google</span>
						</button>

						{/* Separator */}
						<div className="separator mb-5">
							<span className="line"></span>
							<span className="or">OR</span>
							<span className="line"></span>
						</div>
					</div>

					<div className="content has-text-centered">
						{/* ------------------ USERNAME ------------------ */}
						<div className="field">
							<div className="control show-error-message-above" data-err-msg="">
								<input ref={usernameElementRef}
									className="input is-medium"
									type="text"
									id="username"
									name="username"
									placeholder="Full Name" />
							</div>
						</div>
						{/* ------------------ EMAIL ID ------------------ */}
						<div className="field" style={{ marginTop: "2rem" }}>
							<div className="control show-error-message-below" data-err-msg="">
								<input ref={emailElementRef}
									className="input is-medium"
									type="email"
									id="email"
									name="email"
									placeholder="Email ID" />
							</div>
						</div>
						{/* ------------------ PASSWORD ------------------ */}
						<div className="field" style={{ marginTop: "2rem" }}>
							<div className="control show-error-message-below" data-err-msg="">
								<input ref={passwordElementRef}
									className="input is-medium"
									type={type}
									id="password"
									name="password"
									onChange={(e) => setPassword(e.target.value)}
									placeholder="Password" />
								<span className="eye-icon" onClick={handleToggle}>
									<Icon icon={icon} size={25} style={{ color: 'grey-dark' }} />
								</span>
							</div>
						</div>

						<p className="has-text-centered mt-5 terms-condition-text">
							By signing up you
							agree to our <a href="https://abun.com/terms-conditions/" rel={"noreferrer"} target={"_blank"}>Terms & Conditions</a>&nbsp;
							and <a href="https://abun.com/privacy-policy/" rel={"noreferrer"} target={"_blank"}>Privacy Policy</a>.
						</p>

						<button className="button sign-up-btn is-large is-responsive mt-5"
							ref={submitButtonRef}
							disabled={signupUnderway || googleSignupUnderWay}
							onClick={submitSignupData}
							data-err-msg="">
							<span className="is-flex is-align-items-center">Proceed
								{signupUnderway ?
									<LocalIcon iconName={"spinner"} additionalClasses={["icon-white", "ml-3"]} style={{ filter: 'invert(0)' }} />
									:
									<FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />}
							</span>
						</button>
					</div>

					<p className="has-text-black has-text-centered is-size-5 mb-2  mt-6">
						Already have an Account? <Link to={appSumoCode ? `${pageURL['login']}?code=${appSumoCode}` : pageURL['login']} className="has-text-black is-underlined">Login</Link>
					</p>
				</div>


				<div className="right-container">
					<h1 className="has-text-centered is-size-3 has-text-weight-medium">We are your unfair advantage.</h1>
					<p className="sub-title mx-5 has-text-centered">Ours users see guaranteed growth <br /> within 30 days.</p>

					<div className="reviews-container my-5 level" style={{ gap: '24px' }}>
						<div className="mb-4 is-flex is-flex-direction-column is-align-items-center">
							{stars}
							<p className="quote" style={{ color: '#A79577' }}>"my entire team can't stop using it"</p>
						</div>

						<div className="mb-4 is-flex is-flex-direction-column is-align-items-center">
							{stars}
							<p className="quote" style={{ color: '#A79577' }}>"helped us scale & grow"</p>
						</div>

						<div className="mb-4 is-flex is-flex-direction-column is-align-items-center">
							{stars}
							<p className="quote" style={{ color: '#A79577' }}>"biggest value bomb"</p>
						</div>
					</div>

					<div className="columns is-centered is-flex is-justify-content-space-between has-text-centered">
						<div className="column is-half">
							<ul className="feature-list">
								{leftFeatures.map((feature, idx) => (
									<li key={idx}>{feature}</li>
								))}
							</ul>
						</div>
						<div className="column is-half">
							<ul className="feature-list">
								{rightFeatures.map((feature, idx) => (
									<li key={idx}>{feature}</li>
								))}
							</ul>
						</div>
					</div>
				</div>
			</div>

		</div>
	)
}
