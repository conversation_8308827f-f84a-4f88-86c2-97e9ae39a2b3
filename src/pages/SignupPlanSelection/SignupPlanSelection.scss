$page-sides-spacing: 8rem;
$page-sides-spacing-sm: 2rem;
$page-sides-spacing-xl: 18rem;

$first-breakpoint: 1650px;
$second-breakpoint: 1360px;
$third-breakpoint: 836px;
$fourth-breakpoint: 460px;

@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/components/card";
@import "bulma/sass/elements/button";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";
@import "../../assets/bulma-overrides/bulmaOverrides";

.signup-plan-selection-container * {
  font-family: $primary-font;
}

.signup-plan-selection-container {
  display: block;
  width: 100%;
  min-height: 100vh;
  background-image: linear-gradient(180deg, #F1F1F9 0%, #FFFFFF 90%);
  align-items: center;
  justify-content: center;

  .column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
      width: 72vw;
      flex-wrap: wrap;

      @media (max-width:1500px) {
        width: 100%;
        max-width: 1094px;

      }

      @media (max-width:902px) {
        justify-content: center;
        gap: 2rem;
      }

      .header-logo {
        background-color: #ffffff;
        border-radius: 50px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 20px;
        border: 3px solid #000000;
        width: auto;
        height: auto;
        box-shadow: 0px 7px 0px 0px #000000;
        zoom: .75;

        img {
          width: 50%;
          height: auto;
          margin-bottom: .4rem;
        }
      }

      .header-right-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
        gap: 18px;

        .header-text {
          font-size: 2.5rem;
          text-shadow: 1.75px 0 #000000;
          letter-spacing: 1px;
          font-weight: 500;
          color: $black;
        }

        .logout {
          color: #000;
          font-size: 1.2rem;
          border: 1px solid;
          padding: 10px 42px;
          border-radius: 25px;
          font-weight: 500;
          background-color: #fff;
          box-shadow: 1px 2px 0px 0px #000000;
          cursor: pointer;

          &:hover {
            background: #F2CE40;
            box-shadow: 0rem 0rem #00000000;
          }
        }
      }
    }
  }

  .plan-selection-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .offer-card {
      margin: 3rem 0 1rem 0 !important;
      width: 100%;
      max-width: 1094px;

      h2 {
        color: #7A63DF;
        font-family: $primary-font;
        font-size: 20px;
        font-weight: 700;
        letter-spacing: -0.2px;
      }
    }

    // @media (max-width:770px) {
    //   .offer-card{
    //     width: auto;
    //   }

    // }

    .subscription-page-header {

      .tabs {
        background: #fff;
        width: fit-content;
        margin: 0 auto;
        border-radius: 10px;
        padding: 10px;

        @media (max-width: 768px) {
          padding: 8px;
          font-size: 0.9rem;
        }

        @media (max-width: 480px) {
          padding: 6px;
          font-size: 0.85rem;
        }

        ul {
          border-bottom: none;
          gap: 8px;
        }

        li {

          a {
            border-bottom: none;
            border-radius: 9px;
            font-size: 18px;

            @media (max-width: 768px) {
              font-size: 16px;
              padding: 0.4em 0.8em;
            }

            @media (max-width: 480px) {
              font-size: 14px;
              padding: 0.3em 0.6em;
            }

            &:hover {
              background-color: #2E64FE;
              color: $white;
            }
          }

          &.is-active a {
            background-color: #2E64FE;
            color: $white;
          }
        }
      }

    }

    .down-plan-card {

      button {
        border-radius: 50px;
        padding: 10px 20px;
        width: 180px;
        color: #000;
        border: 1px solid #000 !important;
        background-color: #fff;
        transition: transform 0.4s ease;

        &:hover {
          background-color: #2E64FE !important;
          color: #fff !important;
          border: none !important;
          transform: translateY(-5px);
        }

      }

      @media (max-width: 768px) {
        button {
          width: 160px;
          padding: 8px 16px;
          font-size: 0.9rem;
        }
      }

      @media (max-width: 480px) {
        button {
          width: 140px;
          padding: 6px 12px;
          font-size: 0.85rem;
        }
      }
    }


    .plan-cards-container-new-pricing {
      display: grid;
      width: 96%;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      grid-row-gap: 2em;
      // grid-column-gap: 1.5em;
      justify-content: center;

      @media (min-width:1200px) {
        max-width: 1800px;
      }

      @media (max-width:1200px) {
        // grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        row-gap: 1.5rem;
        column-gap: 1rem;
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        width: 100%;
        padding: 0 1rem;
        row-gap: 1rem;
      }

      @media (max-width: 480px) {
        padding: 0 0.5rem;
      }

      .plan-card {
        display: flex;
        flex-direction: column;
        gap: 10px;
        background-color: $white;
        border: 2.75px solid #000;
        border-radius: 0.625rem;
        height: 100%; // Ensure all cards have equal height
        padding: 1.25rem;
        margin: 0; // Remove any default margins

        @media (max-width:1200px) {
          padding: 1rem;
        }

        @media (max-width: 768px) {
          padding: 1rem;
          margin: 0 !important; // Override any inherited margins
        }

        @media (max-width: 480px) {
          padding: 0.75rem;
        }

        // Ensure the main content area takes up available space
        .is-flex.is-flex-direction-column {
          height: 100%;
          display: flex !important;
          flex-direction: column !important;
        }

        // Push the plan-details (which contains the button) to the bottom
        .plan-details {
          margin-top: auto;
          display: flex;
          flex-direction: column;

          // Ensure button is at the bottom of plan-details
          .plan-purchase-button {
            margin-top: auto;
          }
        }

        // @include until($second-breakpoint) {
        //   height: 49rem;
        // }

        // Remove overlapping margins for better mobile experience
        &:first-child,
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4),
        &:last-child {
          z-index: 1;
          margin-left: 0;

          @media (min-width: 1201px) {

            // Only apply overlapping on larger screens where it looks good
            &:nth-child(2) {
              margin-left: -1em;
              z-index: 2;
            }

            &:nth-child(3) {
              margin-left: -1em;
              z-index: 3;
            }
            &:nth-child(4) {
              margin-left: -1em;
              z-index: 4;
            }

            &:last-child {
              margin-left: -1em;
              z-index: 5;
            }
          }
        }

        hr {
          margin: 0.5rem auto;
          border: 0;
          border-top: 1px solid #ddddddaa;
          width: 80%;
        }

        .plan-details .plan-details--item {
          font-size: 1rem;
          padding-bottom: 5px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          text-wrap: nowrap;

          span.icon {
            margin-right: 2px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            max-width: 1.375rem;
            max-height: 1.375rem;
          }
        }

        .plan-details {
          .plan-article-gradient {
            width: fit-content;
            background: repeating-linear-gradient(to right, #d1cfd7, #D1CFD7 2px, transparent 0px, transparent 5px),
              linear-gradient(to top, #C3F2CE 0%, #c3f2ce 48%, transparent 73%, transparent 100%);
            background-size: 100% 2.6px, 100% 100%;
            background-repeat: no-repeat;
            line-height: 1.1;
            background-position: 0px 114%, 0px 6px;
          }

          // Fix for Coming Soon badges alignment
          ul {
            li {
              display: flex;
              align-items: center;
              white-space: nowrap;

              // Ensure proper alignment for Coming Soon items
              .is-flex.is-align-items-center.is-justify-content-space-between {
                width: 100%;
                flex-wrap: nowrap;

                span:first-child {
                  display: flex;
                  align-items: center;
                  flex-shrink: 0;
                  white-space: nowrap;
                }

                .tag {
                  flex-shrink: 0;
                  margin-left: auto;

                  // Large screens (1200px and above)
                  @media (min-width: 1200px) {
                    font-size: 0.6rem !important;
                    padding: 0.18em 0.45em !important;
                  }

                  // Medium screens (768px to 1199px)
                  @media (min-width: 768px) and (max-width: 1199px) {
                    font-size: 0.58rem !important;
                    padding: 0.16em 0.4em !important;
                  }

                  // Small screens (480px to 767px)
                  @media (min-width: 480px) and (max-width: 767px) {
                    font-size: 0.55rem !important;
                    padding: 0.14em 0.35em !important;
                  }

                  // Extra small screens (below 480px)
                  @media (max-width: 479px) {
                    font-size: 0.52rem !important;
                    padding: 0.12em 0.3em !important;
                  }
                }
              }

              // For non-Coming Soon items
              .is-flex.is-align-items-center:not(.is-justify-content-space-between) {
                white-space: nowrap;
              }
            }
          }
        }

        .plan-name {
          font-size: 2.5rem;
          font-weight: 700;
          line-height: 1.1em;
          letter-spacing: -1px;
          color: $black;

          @media (max-width: 768px) {
            font-size: 2rem;
          }

          @media (max-width: 480px) {
            font-size: 1.75rem;
          }
        }

        .plan-card:nth-of-type(3) .cancel-pricing {
          font-size: 0rem !important;
        }

        .cancel-pricing {
          font-weight: 400;
          font-size: 1.875rem;
          line-height: 1.1em;
          color: #8E8E90;
          text-decoration: line-through;
          white-space: nowrap;

          // @media (max-width: 768px) {
          //   font-size: 1.5rem;
          // }

          // @media (max-width: 480px) {
          //   font-size: 1.25rem;
          // }
        }

        hr {
          margin: 1.25rem 0;
          border: 0;
          border-top: 1px solid #e7e7e7;
          width: 80%;
        }

        .pricing {
          font-weight: 700;
          font-size: 3.75rem;
          color: $black;
          line-height: 1.1em;

          // @media (max-width: 768px) {
          //   font-size: 2.5rem;
          // }

          // @media (max-width: 480px) {
          //   font-size: 2rem;
          // }

          .pricing-suffix {
            font-size: 1.125rem;
            font-weight: 400;
            color: $black;
            white-space: nowrap;

            // @media (max-width: 768px) {
            //   font-size: 1rem;
            // }

            // @media (max-width: 480px) {
            //   font-size: 0.875rem;
            // }
          }
        }

        .plan-purchase-button {
          background-color: #ffffff;
          border-radius: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 10px 20px;
          border: 1px solid #000000 !important;
          width: 75%;
          height: auto;
          margin: 1em auto;
          margin-top: 1rem;
          color: $black !important;
          cursor: pointer;
          transition: transform 0.4s ease;
          font-size: 1rem;

          @media (max-width: 768px) {
            width: 85%;
            padding: 12px 24px;
            font-size: 0.95rem;
          }

          @media (max-width: 480px) {
            width: 90%;
            padding: 10px 20px;
            font-size: 0.9rem;
          }

          &:disabled {
            filter: grayscale(0.8);
            cursor: not-allowed;
          }

          &:hover {
            background-color: #2E64FE !important;
            color: #fff !important;
            border: none !important;
            transform: translateY(-5px);
          }
        }

        &.popular {
          position: relative;
          border-radius: 0.625rem;
          border: 5px solid #2E64FE;
          background-color: #FFFFE6;

          @include until($second-breakpoint) {
            transform: scale(1);
          }

          .price {
            color: $black !important;
          }

          // .plan-details .plan-details--item span.icon svg {
          //   ellipse {
          //     fill: #2dce89;
          //   }

          //   path {
          //     fill: #fff;
          //   }
          // }

          .plan-purchase-button {
            background: #2E64FE;
            color: #fff !important;
            transition: transform 0.4s ease;
            border: none !important;

            &:hover {
              transform: translateY(-5px);
            }
          }
        }
      }
    }

    // .plan-cards-container {
    //   display: grid;
    //   width: 96%;
    //   max-width: 1090px;
    //   // grid-template-columns: repeat(4, 1fr);
    //   grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    //   grid-row-gap: 2em;
    //   justify-content: center;
    //   // width: 68.75rem;

    //   // @include until($first-breakpoint) {
    //   //   grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
    //   //   justify-content: center;
    //   // }

    //   @include until($second-breakpoint) {
    //     row-gap: 1rem;
    //     column-gap: 1rem;
    //   }

    //   .plan-card {
    //     display: flex;
    //     flex-direction: column;
    //     justify-content: space-between;
    //     background-color: $white;
    //     border: 3.25px solid #000;
    //     border-radius: 2rem;
    //     padding: 1.25rem;
    //     zoom: .85;

    //     // @include until($second-breakpoint) {
    //     //   height: 49rem;
    //     // }

    //     &:first-child {
    //       z-index: 1;
    //       @include until($second-breakpoint) {
    //         border: 4px solid #000;
    //         border-radius: 2rem;
    //       }
    //     }

    //     &:nth-child(2) {
    //       z-index: 2;
    //       margin-left: -1em;
    //       @include until($second-breakpoint) {
    //         border: 4px solid #000;
    //         border-radius: 2rem;
    //       }
    //     }

    //     &:nth-child(3) {
    //       z-index: 3;
    //       margin-left: -1em;
    //       @include until($second-breakpoint) {
    //         border: 4px solid #000;
    //         border-radius: 2rem;
    //       }
    //     }

    //     &:last-child {
    //       z-index: 5;
    //       margin-left: -1em;

    //       @include until($second-breakpoint) {
    //         border: 4px solid #000;
    //         border-radius: 2rem;
    //       }
    //     }

    //     hr {
    //       margin: 0.5rem auto;
    //       border: 0;
    //       border-top: 1px solid #ddddddaa;
    //       width: 80%;
    //     }

    //     .plan-details .plan-details--item {
    //       font-size: 1rem;
    //       padding-bottom: 5px;
    //       display: flex;
    //       flex-direction: row;
    //       justify-content: flex-start;
    //       align-items: center;
    //       text-wrap: nowrap;

    //       span.icon {
    //         margin-right: 2px;
    //         display: flex;
    //         flex-direction: row;
    //         justify-content: center;
    //         align-items: center;
    //         max-width: 1.375rem;
    //         max-height: 1.375rem;
    //       }
    //     }

    //     .plan-name {
    //       font-size: 2rem;
    //       font-weight: 900;
    //       color: $black;
    //     }

    //     .plan-card:nth-of-type(3) .cancel-pricing {
    //       font-size: 0rem!important;
    //   }

    //     .cancel-pricing{
    //       font-weight: 500;
    //       font-size: 2.375rem;
    //       color: $grey-dark;
    //       text-decoration: line-through;
    //     }

    //     .pricing {
    //       font-weight: 700;
    //       font-size: 3.375rem;
    //       color: $black;

    //       .pricing-suffix {
    //         margin-left: 0.5rem;
    //         font-size: 1.125rem;
    //         font-weight: 400;
    //         color: $black;
    //       }
    //     }

    //     .plan-purchase-button {
    //       background-color: #ffffff;
    //       border-radius: 50px;
    //       display: flex;
    //       align-items: center;
    //       justify-content: center;
    //       padding: 10px 20px;
    //       border: 1px solid #000000;
    //       width: 75%;
    //       height: auto;
    //       box-shadow: 1px 2px 0px 0px #000000;
    //       margin: auto;
    //       margin-top: 1rem;
    //       cursor: pointer;
    //       transition: all 0.3s ease;

    //       &:disabled {
    //         filter: grayscale(0.8);
    //         cursor: not-allowed;
    //       }

    //       &:hover {
    //         transform: translate(-0.25rem, -0.25rem);
    //         background: #fac44b !important;
    //         box-shadow: 0.25rem 0.25rem #000000;
    //       }
    //     }

    //     &.popular {
    //       position: relative;
    //       border-radius: 2rem;
    //       background-color: #DAFFB5;
    //       transform: scale(1.075);
    //       box-shadow: 10px 10px 0px 0px #000000;

    //       @include until($second-breakpoint) {
    //         transform: scale(1);
    //       }

    //       .price {
    //         color: $black;
    //       }

    //       .plan-details .plan-details--item span.icon svg {
    //         ellipse {
    //           fill: #2dce89;
    //         }

    //         path {
    //           fill: #fff;
    //         }
    //       }

    //       .plan-purchase-button {
    //         background: #F2CE40;

    //         &:hover {
    //           transform: translate(0.2rem, 0.2rem);
    //           background-color: #ffffff !important;
    //           color: #000000 !important;
    //           border: 1px solid #000000;
    //           box-shadow: 0.25rem 0.25rem #00000000;
    //         }
    //       }
    //     }
    //   }
    // }

    .feature-list-container {
      display: flex;
      flex-direction: column;

      .hands-free-message-img {
        width: 17rem;
        height: auto;
      }

      .pfp-list-img {
        width: 17rem;
        height: auto;
      }
    }
  }

  .signup-plan-selection--notification-section {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: auto;
  }

  @media screen and (max-height: 900px) {
    zoom: 0.9;
  }

  @media screen and (max-height: 800px) {
    zoom: 0.8;
  }
}

.icon-white {
  filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}

@media (max-width: 1540px) {
  .plan-cards-container-new-pricing .plan-card .pricing {
    font-size: 2.21rem;
    white-space: nowrap;
  }
}

@media (max-width: 600px) {
  .plan-cards-container-new-pricing .plan-card .pricing {
    font-size: 1.8rem;
  }
}