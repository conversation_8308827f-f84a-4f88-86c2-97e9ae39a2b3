import React from 'react';
import './CalculatorLoadingScreen.min.css';

interface CalculatorLoadingScreenProps {
    calculatorType: string;
    message?: string;
}

const CalculatorLoadingScreen: React.FC<CalculatorLoadingScreenProps> = ({
    calculatorType,
    message = "This may take a few moments."
}) => {
    return (
        <div className="calculator-loading-screen">
            <div className="calculator-loading-container">
                <div className="calculator-loading-content">
                    {/* Calculator Icon Animation */}
                    <div className="calculator-icon-container">
                        <svg
                            className="calculator-icon"
                            viewBox="0 0 100 120"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            {/* Calculator Body */}
                            <rect
                                x="10"
                                y="10"
                                width="80"
                                height="100"
                                rx="8"
                                fill="#2E64FE"
                                stroke="#1E4FD9"
                                strokeWidth="2"
                            />

                            {/* Screen */}
                            <rect
                                x="18"
                                y="18"
                                width="64"
                                height="20"
                                rx="4"
                                fill="#000"
                                className="calculator-screen"
                            />

                            {/* Screen Text */}
                            <text
                                x="50"
                                y="32"
                                textAnchor="middle"
                                fill="#00FF00"
                                fontSize="8"
                                fontFamily="monospace"
                                className="calculator-display"
                            >
                                COOKING...
                            </text>

                            {/* Button Grid */}
                            <g className="calculator-buttons">
                                {/* Row 1 */}
                                <circle cx="25" cy="50" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="40" cy="50" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="55" cy="50" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="70" cy="50" r="6" fill="#FFD700" className="calc-button operator" />

                                {/* Row 2 */}
                                <circle cx="25" cy="65" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="40" cy="65" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="55" cy="65" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="70" cy="65" r="6" fill="#FFD700" className="calc-button operator" />

                                {/* Row 3 */}
                                <circle cx="25" cy="80" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="40" cy="80" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="55" cy="80" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="70" cy="80" r="6" fill="#FFD700" className="calc-button operator" />

                                {/* Row 4 */}
                                <rect x="19" y="89" width="22" height="12" rx="6" fill="#FFF" className="calc-button zero" />
                                <circle cx="55" cy="95" r="6" fill="#FFF" className="calc-button" />
                                <circle cx="70" cy="95" r="6" fill="#FF6B6B" className="calc-button equals" />
                            </g>

                            {/* Cooking Steam Animation */}
                            <g className="steam-container">
                                <path
                                    d="M45 8 Q47 5 49 8 Q51 5 53 8"
                                    stroke="#87CEEB"
                                    strokeWidth="2"
                                    fill="none"
                                    className="steam steam-1"
                                />
                                <path
                                    d="M48 6 Q50 3 52 6 Q54 3 56 6"
                                    stroke="#87CEEB"
                                    strokeWidth="2"
                                    fill="none"
                                    className="steam steam-2"
                                />
                                <path
                                    d="M42 9 Q44 6 46 9 Q48 6 50 9"
                                    stroke="#87CEEB"
                                    strokeWidth="2"
                                    fill="none"
                                    className="steam steam-3"
                                />
                            </g>
                        </svg>
                    </div>

                    {/* Loading Text */}
                    <div className="loading-text-container">
                        <h2 className="loading-title">
                            An Amazing {calculatorType} {calculatorType.toLowerCase().endsWith('calculator') ? "" : "Calculator"} is being cooked for your site!
                        </h2>
                        <p className="loading-subtitle">
                            Creating your custom calculator for "{calculatorType}".
                        </p>
                        <p className="loading-message">{message}</p>

                        {/* Loading Dots */}
                        <div className="loading-dots">
                            <span className="dot"></span>
                            <span className="dot"></span>
                            <span className="dot"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CalculatorLoadingScreen;
