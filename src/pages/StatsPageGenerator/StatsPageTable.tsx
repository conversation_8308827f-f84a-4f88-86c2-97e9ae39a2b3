import { useQuery } from '@tanstack/react-query';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import AbunLoader from "../../components/AbunLoader/AbunLoader";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { getAIStatsPagesQuery } from "../../utils/api";
import './StatsPageTable.min.css';

interface AIStatsPage {
    id: number;
    stats_id: string;
    stats_type: string;
    stats_topic: string;
    is_verified: boolean;
    version_count: number;
    created_on: string;
    created_on_relative: string;
    original_keyword?: string;
    original_ideas?: string[];
    selected_idea_index?: number;
    is_processing: boolean;
    is_completed: boolean;
    is_failed: boolean;
}

function StatsPageTable() {
    // --------------------------- CONSTANTS ---------------------------
    const pageSizes = [5, 10, 15, 30, 50, 100, 500];

    // --------------------------- STATES ---------------------------
    const [statsPages, setStatsPages] = useState<AIStatsPage[]>([]);
    const [loadingRowId, setLoadingRowId] = useState<string | null>(null);

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // --------------------------- NAVIGATION ---------------------------
    const navigate = useNavigate();

    // --------------------------- QUERIES ---------------------------

    // Query for getting all stats pages
    const {
        isLoading: isLoadingPages,
        error: pagesError,
        data: pagesData
    } = useQuery({
        ...getAIStatsPagesQuery()
    });

    // Handle pages data processing
    useEffect(() => {
        if (pagesData) {
            let pages: AIStatsPage[] = [];

            if ((pagesData as any)?.status === 'success' && (pagesData as any)?.data?.pages) {
                pages = (pagesData as any).data.pages;
            } else if ((pagesData as any)?.data?.status === 'success' && (pagesData as any)?.data?.data?.pages) {
                pages = (pagesData as any).data.data.pages;
            } else if (Array.isArray((pagesData as any)?.data)) {
                pages = (pagesData as any).data;
            } else if (Array.isArray((pagesData as any)?.pages)) {
                pages = (pagesData as any).pages;
            }

            setStatsPages(pages);
        }
    }, [pagesData]);

    // Handle pages error
    useEffect(() => {
        if (pagesError) {
            console.error('Error loading stats pages:', pagesError);
            errorAlertRef.current?.show("Failed to load stats pages");
        }
    }, [pagesError]);

    // --------------------------- UTILITY FUNCTIONS ---------------------------
    const formatRelativeTime = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMs = now.getTime() - date.getTime();
        const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
        const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
        const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

        // Less than 5 minutes
        if (diffInMinutes < 5) {
            return "few minutes ago";
        }

        // 5-59 minutes (in 10-minute intervals)
        if (diffInMinutes < 60) {
            const roundedMinutes = Math.floor(diffInMinutes / 10) * 10;
            return `${roundedMinutes} mins ago`;
        }

        // 1 hour
        if (diffInHours === 1) {
            return "an hour ago";
        }

        // 2-23 hours
        if (diffInHours < 24) {
            return `${diffInHours} hours ago`;
        }

        // 1-29 days
        if (diffInDays < 30) {
            return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
        }

        // 30+ days - show formatted date (13, May 25)
        const day = date.getDate();
        const monthNames = [
            "Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
        ];
        const month = monthNames[date.getMonth()];
        const year = date.getFullYear().toString().slice(-2); // Get last 2 digits

        return `${day}, ${month} ${year}`;
    };

    // Helper function to truncate text with ellipsis on last 3 characters
    const truncateText = (text: string, maxLength: number = 80) => {
        if (text.length <= maxLength) {
            return text;
        }

        // Show ellipsis only on the last 3 characters
        const truncated = text.substring(0, maxLength - 3);
        return truncated + '...';
    };

    const handleViewStatsPage = (statsPage: AIStatsPage) => {
        if (!statsPage.stats_id) {
            errorAlertRef.current?.show("Invalid stats page data");
            return;
        }

        setLoadingRowId(statsPage.stats_id);
        navigate(`/ai-stats-page-generator/${statsPage.stats_id}`);
    };

    // --------------------------- TABLE CONFIG ---------------------------
    const columnHelper = createColumnHelper<AIStatsPage>();

    const columnDefs: ColumnDef<AIStatsPage, any>[] = [
        columnHelper.accessor('stats_topic', {
            header: "Stats Page Title",
            cell: (info) => {
                const fullText = info.getValue();
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.stats_id;
                const displayText = truncateText(fullText, 80);

                return (
                    <div
                        className={`stats-title-cell ${isRowLoading ? 'loading' : ''}`}
                        style={{
                            maxWidth: '600px',
                            display: 'flex',
                            alignItems: 'center',
                            opacity: isRowLoading ? 0.6 : 1,
                            pointerEvents: 'none',
                            width: '100%',
                            height: '100%'
                        }}
                        title={fullText}
                    >
                        {isRowLoading && (
                            <Icon iconName="spinner" marginClass="mr-2" />
                        )}
                        <span style={{ pointerEvents: 'none' }}>{displayText}</span>
                    </div>
                );
            }
        }),
        columnHelper.accessor('created_on', {
            header: "Created On",
            cell: (info) => {
                const row = info.row.original;
                const isRowLoading = loadingRowId === row.stats_id;

                return (
                    <div style={{
                        opacity: isRowLoading ? 0.6 : 1,
                        pointerEvents: 'none',
                        width: '100%',
                        height: '100%'
                    }}>
                        {formatRelativeTime(info.getValue())}
                    </div>
                );
            }
        }),
        columnHelper.accessor('is_processing', {
            header: "Status",
            cell: (info) => {
                const row = info.row.original;
                const isProcessing = row.is_processing;
                const isFailed = row.is_failed;

                if (isProcessing) {
                    return (
                        <span style={{ color: '#3273dc', fontWeight: '500' }}>
                            Generating...
                        </span>
                    );
                } else if (isFailed) {
                    return (
                        <span style={{ color: '#ff3860', fontWeight: '500' }}>
                            Failed
                        </span>
                    );
                } else {
                    return (
                        <span style={{ color: '#23d160', fontWeight: '500' }}>
                            Generated
                        </span>
                    );
                }
            }
        }),
        columnHelper.accessor('stats_id', {
            header: "Action",
            cell: (info) => {
                const row = info.row.original;
                const isProcessing = row.is_processing;
                const isFailed = row.is_failed;

                if (isProcessing) {
                    return (
                        <span style={{ color: '#3273dc', fontWeight: '500' }}>
                            Generating...
                        </span>
                    );
                } else if (isFailed) {
                    return (
                        <span style={{ color: '#ff3860', fontWeight: '500', cursor: 'pointer' }}>
                            Failed, Retry
                        </span>
                    );
                } else {
                    return (
                        <button
                            className="button is-primary is-small"
                            onClick={(e) => {
                                e.stopPropagation();
                                handleViewStatsPage(row);
                            }}
                        >
                            View
                        </button>
                    );
                }
            }
        })
    ];

    return (
        <div className="ai-stats-table-tp-container">


            <div className="seo-project-abun-table">

                <div className="table-container">
                    {isLoadingPages ? (
                        <AbunLoader show={isLoadingPages} height="400px" />
                    ) : (
                        <AbunTable
                            tableContentName="AI Stats Pages"
                            tableData={statsPages}
                            columnDefs={columnDefs}
                            pageSizes={pageSizes}
                            initialPageSize={pageSizes[1]}
                            noDataText="No Stats Pages Found. Create your first stats page to get started!"
                            searchboxPlaceholderText="Search stats pages..."
                            handleRowClick={(row) => handleViewStatsPage(row)}
                            hidePagination={statsPages.length === 0}
                        />
                    )}
                </div>
            </div>

            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </div>
    );
}

export default withAdminAndProductionCheck(StatsPageTable);