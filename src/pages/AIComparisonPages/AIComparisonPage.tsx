import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Player } from "@lottiefiles/react-lottie-player";
import { useMutation, useQuery } from '@tanstack/react-query';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams, useRouteLoaderData, useSearchParams } from 'react-router-dom';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { BasePageData } from "../../pages/Base/Base";
import { withAdminAndProductionCheck } from '../../utils/adminAndProductionCheck';
import {
    checkJobStatus,
    deleteComparisonPageVersionMutation,
    generateAIComparisonPageMutation,
    getAIComparisonPageDataQuery,
    getAIComparisonPagesQuery,
    modifyAIComparisonPageMutation,
    setCurrentComparisonVersionFn,
    verifyToolsLoadingScriptMutation,
} from "../../utils/api";
import { pageURL } from "../routes";
import './AIComparisonPage.min.css';
import AIComparisonTable from "./AIComparisonTable";

interface ComparisonDataType {
    comp_id: string;
    html_content: string;
    url1: string;
    url2: string;
    current_version_id?: number;
    encrypted_id?: string;
    encrypted_tool_id?: string;
    ai_comparison_page_script_verified?: boolean;
    is_processing: boolean;
    is_completed: boolean;
    is_failed: boolean;
}

interface LocationState {
    compId?: string;
    comparisonType?: string;
    url1?: string;
    url2?: string;
    fromOtherComparison?: boolean;
    fromExisting?: boolean;
    fromCustomUrls?: boolean;
    userModifications?: string[];
    comparisonData?: any; // Data passed from AIComparisonTable to avoid redundant API calls
    navigationTimestamp?: number;
}

interface ComparisonPageListApiResponse {
    status: "success" | "error";
    data: {
        pages: any[];
        comparison_pages_generated: number;
        max_comparison_pages_allowed: number;
    };
    message?: string;
}

interface ComparisonVersion {
    id: number;
    version_name: string;
    html_code: string;
    changes_summary?: string;
    created_on: string;
    created_on_relative: string;
    code_length?: number;
    code_preview?: string;
}

function AIComparisonPage() {
    const basePageData = useRouteLoaderData("base") as BasePageData;
    const { active_website_domain } = basePageData;

    const { comp_id, taskId } = useParams<{ comp_id?: string; taskId?: string }>();
    const navigate = useNavigate();
    const location = useLocation();
    const state = location.state as LocationState | null;

    // Get version from URL search params
    const [searchParams, setSearchParams] = useSearchParams();

    // Refs 
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const isUpdatingRef = useRef(false);

    // Generator states
    const [url1, setUrl1] = useState<string>(active_website_domain || '');
    const [url2, setUrl2] = useState<string>('');
    const [htmlContent, setHtmlContent] = useState<string>('');
    const [compId, setCompId] = useState<string>('');
    const [comparisonData, setComparisonData] = useState<ComparisonDataType | null>(null);
    const [isGenerating, setIsGenerating] = useState(false);
    // Initialize showComparisonPage based on whether we have a compId (from URL or navigation state)
    const [versions, setVersions] = useState<ComparisonVersion[]>([]);
    const [currentVersionId, setCurrentVersionId] = useState<number | null>(null);
    const [currentTaskId, setCurrentTaskId] = useState<string | null>(taskId || null);
    const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
    const [versionToDelete, setVersionToDelete] = useState<number | null>(null);
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
    const [updateTabOpen, setUpdateTabOpen] = useState(true);
    const [versionsTabOpen, setVersionsTabOpen] = useState(false);
    const [embedTabOpen, setEmbedTabOpen] = useState(false);
    const [verifyTabOpen, setVerifyTabOpen] = useState(false);
    const [userInput, setUserInput] = useState('');
    const [isUpdating, setIsUpdating] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [isVerified, setIsVerified] = useState(false);
    const [comparisonScriptTag, setComparisonScriptTag] = useState("");
    const [comparisonDivTag, setComparisonDivTag] = useState("");
    const [activeTab, setActiveTab] = useState("ai-comparison");



    // Mutations
    const generateComparisonMutation = useMutation(generateAIComparisonPageMutation);

    const modifyPageMutation = useMutation({
        ...modifyAIComparisonPageMutation
    });

    // Mutation for verifying script
    const verifyScriptMutation = useMutation(verifyToolsLoadingScriptMutation);

    // Queries
    // Only fetch comparison page list when we're not viewing a specific comparison page
    // (i.e., when creating new comparison pages)
    const isViewingSpecificComparisonPage = !!(comp_id || state?.compId);

    const {
        data: comparisonPageListData,
        isLoading: __,
        error: _
    } = useQuery<ComparisonPageListApiResponse>({
        ...getAIComparisonPagesQuery(),
        enabled: !isViewingSpecificComparisonPage, // Only run when not viewing a specific comparison page
    });

    // Job status polling query
    useQuery({
        ...checkJobStatus(currentTaskId || ''),
        enabled: !!currentTaskId && (isGenerating || isUpdating),
        refetchInterval: 3000, // Poll every 3 seconds
        onSuccess: (response: any) => {
            if (response?.data?.status === "completed") {
                // Job completed successfully
                const wasUpdating = isUpdating;

                // Clear states
                setIsGenerating(false);
                setIsUpdating(false);
                setCurrentTaskId(null);



                if (wasUpdating) {
                    successAlertRef.current?.show("Comparison page updated successfully!");
                } else {
                    successAlertRef.current?.show("Comparison page generated successfully!");
                }

                // Refresh the page data if needed
                window.location.reload();
            } else if (response?.data?.status === "failed") {
                // Job failed - redirect to table page
                setIsGenerating(false);
                setIsUpdating(false);
                setCurrentTaskId(null);

                errorAlertRef.current?.show("Task failed. Redirecting to comparison pages list...");
                setTimeout(() => {
                    errorAlertRef.current?.close();
                    navigate(pageURL.AIComparisonTable);
                }, 2000);
            }
        },
        onError: () => {
            // Handle query error
            setIsGenerating(false);
            setIsUpdating(false);
            setCurrentTaskId(null);
            errorAlertRef.current?.show("Failed to check job status. Please refresh the page.");
        }
    });

    // Fix: Remove !state?.compId from isLoading calculation for generator view
    const isLoading = generateComparisonMutation.isLoading || modifyPageMutation.isLoading || isGenerating || isUpdating;

    const canGenerateMore = useMemo(() => {
        return (comparisonPageListData?.data?.comparison_pages_generated || 0) < (comparisonPageListData?.data?.max_comparison_pages_allowed || 5);
    }, [comparisonPageListData?.data?.comparison_pages_generated, comparisonPageListData?.data?.max_comparison_pages_allowed]);

    const handleGenerate = useCallback(() => {
        if (!canGenerateMore) {
            errorAlertRef.current?.show(
                `You have reached your monthly limit of ${comparisonPageListData?.data?.max_comparison_pages_allowed || 5} comparison pages. Please upgrade your plan to generate more.`
            );
            return;
        }

        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if (!url1.trim() || !url2.trim()) {
            errorAlertRef.current?.show('Please enter both URLs');
            return;
        }

        const normalizeUrl = (url) => {
            url = url.trim();
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                return 'https://' + url;
            }
            return url;
        };

        const normalizedUrl1 = normalizeUrl(url1);
        const normalizedUrl2 = normalizeUrl(url2);

        setIsGenerating(true);

        generateComparisonMutation.mutate(
            {
                url1: normalizedUrl1,
                url2: normalizedUrl2,
            },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data && (data.status === 'success' || data.status === 'processing')) {
                        if (data.task_id && data.comp_id) {
                            setCurrentTaskId(data.task_id);
                            setCompId(data.comp_id);



                            navigate(`/ai-comparison-generator/${data.comp_id}`, { replace: true });

                            successAlertRef.current?.show("Comparison page generation started...");

                        } else {
                            console.error('Missing task_id or comp_id in response:', data);
                            setIsGenerating(false);
                            errorAlertRef.current?.show("Server response missing required data");
                        }
                    } else {
                        console.error('Unexpected response format:', data);
                        setIsGenerating(false);
                        errorAlertRef.current?.show("Unexpected response from server");
                    }
                },
                onError: (error) => {
                    console.error('Generation failed:', error);

                    setIsGenerating(false);
                    setCurrentTaskId(null);

                    navigate('/ai-comparison-generator', { replace: true });

                    let errorMessage = "Failed to generate comparison page. Please try again.";
                    if (error?.response?.data?.message) {
                        errorMessage = error.response.data.message;
                    }
                    errorAlertRef.current?.show(errorMessage);
                }
            }
        );
    }, [canGenerateMore, comparisonPageListData, url1, url2, generateComparisonMutation, navigate, errorAlertRef, successAlertRef]);

    const handleUpdateComparisonPage = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if (!userInput.trim() || !compId) {
            errorAlertRef.current?.show("Please enter modifications and ensure comparison page exists");
            return;
        }

        setIsUpdating(true);

        let currentHtmlContent = htmlContent;

        if (iframeRef.current) {
            const iframe = iframeRef.current;
            const doc = iframe.contentDocument || iframe.contentWindow?.document;
            if (doc) {
                currentHtmlContent = doc.documentElement.outerHTML;
                setHtmlContent(currentHtmlContent);
            }
        }

        modifyPageMutation.mutate(
            {
                comp_id: compId,
                modifications: userInput.trim(),
                html_content: currentHtmlContent
            },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data && data.status === 'processing' && data.task_id) {
                        // Set the task ID for job status tracking
                        setCurrentTaskId(data.task_id);



                        successAlertRef.current?.show("Comparison page modification started. Please wait...");
                    } else {
                        setIsUpdating(false);
                        errorAlertRef.current?.show("Failed to start modification task");
                    }
                },
                onError: (error: any) => {
                    console.error('Update failed:', error);
                    setIsUpdating(false);

                    let errorMessage = "Failed to update comparison page.";

                    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
                        errorMessage = "Request timed out. The server might still be processing. Please wait a moment and check if the update was applied.";
                    } else if (error.message?.includes('broken pipe')) {
                        errorMessage = "Connection was interrupted. Please try again.";
                    } else if (error.response?.status === 404) {
                        errorMessage = "API endpoint not found. Make sure DEBUG mode is enabled.";
                    } else if (error.response?.data?.err_id === 'NO_COMPARISON_PAGE_FOUND') {
                        errorMessage = "Comparison page not found. Please generate a new one.";
                    } else if (error.response?.data?.message) {
                        errorMessage = error.response.data.message;
                    }

                    errorAlertRef.current?.show(errorMessage);
                }
            }
        );
    };

    const handleSaveDirectHtmlChanges = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if (!compId) {
            errorAlertRef.current?.show("Comparison page not found");
            return;
        }

        let currentHtmlContent = htmlContent;

        if (iframeRef.current) {
            const iframe = iframeRef.current;
            const doc = iframe.contentDocument || iframe.contentWindow?.document;
            if (doc) {
                currentHtmlContent = doc.documentElement.outerHTML;
            }
        }

        if (!currentHtmlContent.trim()) {
            errorAlertRef.current?.show("No content to save");
            return;
        }

        setIsUpdating(true);

        modifyPageMutation.mutate(
            {
                comp_id: compId,
                modifications: "Direct HTML content update",
                html_content: currentHtmlContent.trim()
            },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data && data.status === 'processing' && data.task_id) {
                        // Set the task ID for job status tracking
                        setCurrentTaskId(data.task_id);



                        successAlertRef.current?.show("Saving changes. Please wait...");
                    } else {
                        setIsUpdating(false);
                        errorAlertRef.current?.show("Failed to start modification task");
                    }
                },
                onError: (error) => {
                    console.error('Direct HTML save failed:', error);
                    setIsUpdating(false);
                    errorAlertRef.current?.show("Failed to save changes");
                }
            }
        );
    };

    // Set script tags and verification status when comparison data is available
    useEffect(() => {
        if (comparisonData?.encrypted_id && comparisonData?.encrypted_tool_id) {
            setComparisonScriptTag(`<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${comparisonData.encrypted_id}" data-tool-name="ai-comparison-page"></script>`);
            setComparisonDivTag(`<div data-ai-comparison-page-id="${comparisonData.encrypted_tool_id}"></div>`);
        }

        // Set verification status
        if (comparisonData?.ai_comparison_page_script_verified !== undefined) {
            setIsVerified(comparisonData.ai_comparison_page_script_verified);
        }
    }, [comparisonData]);

    useEffect(() => {
        // Set script tags and verification status from navigation state (fallback)
        if (state?.comparisonData?.encrypted_id && state?.comparisonData?.encrypted_tool_id) {
            setComparisonScriptTag(`<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${state?.comparisonData.encrypted_id}" data-tool-name="ai-comparison-page"></script>`);
            setComparisonDivTag(`<div data-ai-comparison-page-id="${state?.comparisonData.encrypted_tool_id}"></div>`);
        }

        // Set verification status from navigation state (fallback)
        if (state?.comparisonData?.ai_comparison_page_script_verified !== undefined) {
            setIsVerified(state?.comparisonData.ai_comparison_page_script_verified);
        }

        if (state?.fromOtherComparison && state?.compId) {
            setCompId(state.compId);
            setUrl1(state.url1 || '');
            setUrl2(state.url2 || '');

        } else if (state?.fromExisting && state?.compId) {
            setCompId(state.compId);
            setUrl1(state.url1 || '');
            setUrl2(state.url2 || '');

            // Set the comparison data and HTML content from navigation state
            if (state.comparisonData) {
                // Set versions from navigation state
                if (state.comparisonData.versions && Array.isArray(state.comparisonData.versions)) {
                    setVersions(state.comparisonData.versions);
                }

                // Set current version ID
                if (state.comparisonData.current_version_id) {
                    setCurrentVersionId(state.comparisonData.current_version_id);
                } else if (state.comparisonData.latest_version?.id) {
                    setCurrentVersionId(state.comparisonData.latest_version.id);
                }

                // Handle different possible field names for HTML content
                const htmlContent = state.comparisonData.html_content ||
                    state.comparisonData.current_html_content ||
                    state.comparisonData.latest_version?.html_code ||
                    '';

                if (htmlContent) {
                    setHtmlContent(htmlContent);
                } else {
                    console.warn('No HTML content found in comparisonData');
                }

                // Create properly structured comparison data
                const comparisonDataObj: ComparisonDataType = {
                    comp_id: state.comparisonData.comp_id || state.compId,
                    html_content: htmlContent,
                    url1: state.comparisonData.url1 || state.comparisonData.url1_domain || '',
                    url2: state.comparisonData.url2 || state.comparisonData.url2_domain || '',
                    current_version_id: state.comparisonData.current_version_id || state.comparisonData.latest_version?.id,
                    encrypted_id: state.comparisonData.encrypted_id,
                    encrypted_tool_id: state.comparisonData.encrypted_tool_id,
                    ai_comparison_page_script_verified: state.comparisonData.ai_comparison_page_script_verified,
                    is_processing: state.comparisonData.is_processing || false,
                    is_completed: state.comparisonData.is_completed || false,
                    is_failed: state.comparisonData.is_failed || false
                };

                setComparisonData(comparisonDataObj);
            }

            // Show the comparison page since we have the data

        } else if (state?.fromCustomUrls && state?.url1 && state?.url2) {
            setUrl1(state.url1);
            setUrl2(state.url2);
            // Generation will be handled by the separate useEffect
        }
    }, [state]);

    useEffect(() => {
        if (taskId && !currentTaskId) {
            setCurrentTaskId(taskId);
        }
    }, [taskId, currentTaskId]);


    // Query to fetch specific comparison page data when viewing a comparison page
    useQuery({
        ...getAIComparisonPageDataQuery(comp_id || state?.compId || ''),
        enabled: isViewingSpecificComparisonPage && !!(comp_id || state?.compId),
        onSuccess: (response: any) => {
            if (response?.data?.status === 'success') {
                const data = response.data.data;

                // Set versions from API response
                if (data.versions && Array.isArray(data.versions)) {
                    console.log('Setting versions from API:', data.versions);
                    setVersions(data.versions);
                } else {
                    console.log('No versions found in API response:', data);
                }

                // Set current version ID
                if (data.current_version_id) {
                    setCurrentVersionId(data.current_version_id);
                } else if (data.latest_version?.id) {
                    setCurrentVersionId(data.latest_version.id);
                }

                // Set HTML content
                if (data.html_content) {
                    setHtmlContent(data.html_content);
                } else if (data.latest_version?.html_code) {
                    setHtmlContent(data.latest_version.html_code);
                }

                // Set comparison data
                const comparisonDataObj: ComparisonDataType = {
                    comp_id: data.comp_id,
                    html_content: data.html_content || data.latest_version?.html_code || '',
                    url1: data.url1,
                    url2: data.url2,
                    current_version_id: data.current_version_id || data.latest_version?.id,
                    encrypted_id: data.encrypted_id,
                    encrypted_tool_id: data.encrypted_tool_id,
                    ai_comparison_page_script_verified: data.ai_comparison_page_script_verified,
                    is_processing: data.is_processing || false,
                    is_completed: data.is_completed || false,
                    is_failed: data.is_failed || false
                };
                setComparisonData(comparisonDataObj);

                // Set URLs
                setUrl1(data.url1 || '');
                setUrl2(data.url2 || '');
                setCompId(data.comp_id);
            }
        },
        onError: (error) => {
            console.error('Failed to load comparison page data:', error);
            errorAlertRef.current?.show("Failed to load comparison page data");
        }
    });



    // Safety timeout to prevent infinite loading (15 minutes max)
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        if ((isGenerating || isUpdating) && currentTaskId) {
            timeoutId = setTimeout(() => {
                setIsGenerating(false);
                setIsUpdating(false);
                setCurrentTaskId(null);



                errorAlertRef.current?.show("Task is taking longer than expected. Please try again or check your comparison pages list.");
            }, 15 * 60 * 1000); // 15 minutes
        }

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [isGenerating, isUpdating, currentTaskId, compId]);

    const lastKnownContent = useRef('');

    useEffect(() => {
        if (iframeRef.current && htmlContent && !isUpdatingRef.current) {
            const iframe = iframeRef.current;
            const doc = iframe.contentDocument || iframe.contentWindow?.document;

            if (doc) {
                const currentDocContent = doc.documentElement.outerHTML;

                if (currentDocContent !== htmlContent && lastKnownContent.current !== htmlContent) {
                    doc.open();
                    doc.write(htmlContent);
                    doc.close();
                    lastKnownContent.current = htmlContent;
                }

                if (doc.body && !isUpdating && !modifyPageMutation.isLoading) {
                    doc.body.contentEditable = isEditMode ? 'true' : 'false';

                    if (isEditMode) {
                        doc.body.style.cursor = 'text';
                    } else {
                        doc.body.style.cursor = 'default';
                    }

                    const existingListener = doc.body.getAttribute('data-listener-added');
                    if (!existingListener && isEditMode) {
                        doc.body.setAttribute('data-listener-added', 'true');

                        let updateTimeout;
                        doc.body.addEventListener('input', (e) => {
                            clearTimeout(updateTimeout);

                            updateTimeout = setTimeout(() => {
                                if (!isUpdatingRef.current) {
                                    isUpdatingRef.current = true;
                                    const newContent = doc.documentElement.outerHTML;

                                    if (newContent !== lastKnownContent.current) {
                                        setHtmlContent(newContent);
                                        lastKnownContent.current = newContent;
                                    }

                                    setTimeout(() => {
                                        isUpdatingRef.current = false;
                                    }, 100);
                                }
                            }, 500);
                        });
                    }
                } else if (doc.body && !isEditMode) {
                    doc.body.contentEditable = 'false';
                    doc.body.style.cursor = 'default';
                }
            }
        }
    }, [htmlContent, isUpdating, modifyPageMutation.isLoading, isEditMode]);

    const switchToVersion = async (versionId: number) => {
        const selectedVersion = versions.find(v => v.id === versionId);
        if (!selectedVersion || !comparisonData) {
            console.error('Selected version or comparisonData not found');
            return;
        }

        try {
            setCurrentVersionId(versionId);
            setHtmlContent(selectedVersion.html_code);
            lastKnownContent.current = selectedVersion.html_code;

            const updatedComparisonData: ComparisonDataType = {
                ...comparisonData,
                html_content: selectedVersion.html_code,
                current_version_id: versionId
            };
            setComparisonData(updatedComparisonData);

            // Update URL with selected version to persist across reloads
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set('version', versionId.toString());
            setSearchParams(newSearchParams, { replace: true });

            const response = await setCurrentComparisonVersionFn(comparisonData.comp_id, versionId);

            if (response.status !== 200 && response.data?.status !== 'success') {
                console.error('Backend update failed:', response);
            }
        } catch (error) {
            console.error('Failed to switch version:', error);
        }
    };

    useEffect(() => {
        if (currentVersionId && versions.length > 0) {
            const savedVersion = versions.find(v => v.id === currentVersionId);
            if (savedVersion && savedVersion.html_code !== htmlContent) {
                setHtmlContent(savedVersion.html_code);
            } else if (!savedVersion) {
                console.warn(`Version ${currentVersionId} not found in versions array`);
            }
        }
    }, [currentVersionId, versions]);

    const backToList = () => {
        navigate(-1);
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            successAlertRef.current?.show("Copied to clipboard!");
        }).catch(err => {
            errorAlertRef.current?.show("Failed to copy to clipboard.");
        });
    };


    // Delete comparison page version mutation
    const deleteComparisonPageVersionMut = useMutation({
        ...deleteComparisonPageVersionMutation,
        onSuccess: (data, variables) => {
            // Handle successful deletion
            const deletedVersionId = variables.version_id;

            // Remove from frontend state
            setVersions(prev => prev.filter(v => v.id !== deletedVersionId));

            // If current version is deleted, switch to the most recent one
            if (currentVersionId === deletedVersionId) {
                const remainingVersions = versions.filter(v => v.id !== deletedVersionId);
                if (remainingVersions.length > 0) {
                    switchToVersion(remainingVersions[0].id);
                }
            }

            successAlertRef.current?.show("Version deleted successfully!");
        },
        onError: (error, variables) => {
            console.error('Error deleting version:', error);

            // Handle specific error cases
            if (error?.response?.data?.err_id === 'CANNOT_DELETE_LAST_VERSION') {
                errorAlertRef.current?.show("Cannot delete the last remaining version");
            } else if (error?.response?.data?.err_id === 'VERSION_NOT_FOUND') {
                errorAlertRef.current?.show("Version not found or access denied");
            } else {
                errorAlertRef.current?.show("Failed to delete version. Please try again.");
            }
        }
    });

    // Delete version function
    const deleteVersion = (versionId: number) => {
        // Check if this is the last version
        if (versions.length <= 1) {
            errorAlertRef.current?.show("Cannot delete the last remaining version");
            return;
        }

        // Show the confirmation modal instead of window.confirm
        setVersionToDelete(versionId);
        setShowDeleteModal(true);
    };

    // Handle delete confirmation
    const handleConfirmDelete = async () => {
        if (versionToDelete !== null) {
            // Trigger the mutation
            deleteComparisonPageVersionMut.mutate({ version_id: versionToDelete });

            // Reset state
            setShowDeleteModal(false);
            setVersionToDelete(null);
        }
    };

    // Handle delete cancellation
    const handleCancelDelete = () => {
        setShowDeleteModal(false);
        setVersionToDelete(null);
    };


    // Helper function to get snippet filename based on environment
    const getSnippetFileName = () => {
        if (process.env.REACT_APP_DRF_DOMAIN === 'https://api.abun.com') {
            return 'ai-comparison-snippet.js';
        } else if (process.env.REACT_APP_DRF_DOMAIN === 'https://staging.api.abun.com') {
            return 'ai-comparison-snippet-staging.js';
        } else {
            return 'ai-comparison-snippet-dev.js';
        }
    };

    // Verification handler
    const handleVerifyScript = () => {
        setIsVerifying(true);

        verifyScriptMutation.mutate({ toolName: "ai-comparison-page" }, {
            onSuccess: (response: any) => {
                setIsVerifying(false);
                const responseData = response.data;

                if (responseData.success) {
                    setIsVerified(true);
                    successAlertRef.current?.show("Script verification successful! Your website is properly configured.");
                    setTimeout(() => {
                        successAlertRef.current?.close();
                    }, 5000);
                } else {
                    // Handle different error types based on err_id
                    let errorMessage = responseData.message || "Verification failed";

                    switch (responseData.err_id) {
                        case "NO_WEBSITE_FOUND":
                            errorMessage = "No website found. Please ensure your website is properly connected.";
                            break;
                        case "WEBSITE_NOT_ACCESSIBLE":
                            errorMessage = "Website not accessible. Please check if your website is online and accessible.";
                            break;
                        case "SCRIPT_TAG_NOT_FOUND":
                            errorMessage = "Script tag not found on your website. Please ensure the script is properly installed.";
                            break;
                        case "INVALID_USER_EMAIL":
                            errorMessage = "Invalid user email. Please contact support if this issue persists.";
                            break;
                        default:
                            errorMessage = responseData.message || "Verification failed. Please try again.";
                    }

                    errorAlertRef.current?.show(errorMessage);
                    setTimeout(() => {
                        errorAlertRef.current?.close();
                    }, 5000);
                }
            },
            onError: (error: any) => {
                setIsVerifying(false);
                console.error('Error verifying script:', error);

                const errorMessage = error?.response?.data?.message || "Failed to verify script. Please try again.";
                errorAlertRef.current?.show(errorMessage);
                setTimeout(() => {
                    errorAlertRef.current?.close();
                }, 5000);
            }
        });
    };

    const showGeneratorView = !comp_id;
    const showPageView = !!comp_id;

    return (
        <>
            {/* Generator View - Direct JSX instead of component */}
            {showGeneratorView && (
                <div className="ai-comparison-generator-container">
                    <div className="">
                        <div className="mb-5 is-flex-wrap-wrap ai-comparison-header is-flex is-justify-content-space-between">
                            <div className={"is-flex is-justify-content-center is-flex-direction-column "}>
                                <h2>AI Competitor Comparison Page Generator</h2>
                                <p className={"comparison-p has-text-dark"}>Compare websites and generate detailed comparison content</p>
                            </div>
                            <span className="mt-3">
                            </span>
                        </div>
                        <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                            <ul>
                                <li className={activeTab === "ai-comparison" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("ai-comparison")}>AI comparison Pages</a>
                                </li>
                                <li className={activeTab === "projects" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("projects")}>Projects</a>
                                </li>
                            </ul>
                        </div>

                        {activeTab === "ai-comparison" &&
                            <div className={" is-flex is-align-items-center is-flex-direction-column ai-comparison-container"}>
                                <h3 >Let's compare the AI Comparison Pages</h3>
                                <hr />
                                <div className="comparison-form w-100">
                                    <div className="field">
                                        <label className="ca-label has-text-black label">Your Website</label>
                                        <div className="control">
                                            <input
                                                id="url1"
                                                type="url"
                                                className="input ai-comparison-url-input"
                                                value={url1}
                                                onChange={(e) => setUrl1(e.target.value)}
                                                placeholder={url1 === '' ? "Draftss or https://draftss.com" : ""}
                                                // style={{ marginTop: '3px', width: "60%" }}
                                                disabled={isLoading}
                                            />
                                        </div>
                                    </div>
                                    <div className="field" >
                                        <label className="ca-label has-text-black label">Your Competitors Website or Brand</label>
                                        {/* <span className="text-muted"> </span><br /> */}
                                        <input
                                            id="url2"
                                            type="url"
                                            value={url2}
                                            className="input ai-comparison-url-input mb-3"
                                            placeholder="Draftss or https://draftss.com"
                                            onChange={(e) => setUrl2(e.target.value)}
                                            disabled={isLoading}
                                            style={{ marginTop: '3px' }}
                                        />
                                    </div>
                                    <button type={"submit"} className="mt-2 button is-responsive is-link" style={{ width: 'fit-content' }} disabled={isLoading || !url1.trim() || !url2.trim()}
                                        onClick={handleGenerate}>
                                        {isLoading ? (
                                            "Generating..."
                                        ) : (
                                            <>
                                                Generate <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        }

                        {activeTab === "projects" &&
                            <AIComparisonTable />
                        }

                    </div>
                </div>
            )}

            {showPageView && (
                <div className={`ai-comparison-page-container full-comparison-view`}>
                    {/* Header section - Always visible */}
                    <div className={`card ai-comparison-page-header comparison-view`}>
                        <div className="left-header-section">
                            <span className="back-btn" onClick={backToList}>
                                <svg
                                    className="back-btn"
                                    width="30"
                                    height="24"
                                    viewBox="0 0 30 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                                </svg>
                            </span>

                            <a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
                                <svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48">
                                    <rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" strokeWidth="3" />
                                    <rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
                                    <path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
                                </svg>
                            </a>

                            <div className="Tabs">
                                <div className="Tab active">
                                    Comparison Page
                                </div>
                            </div>
                        </div>

                        <div className="right-header-section">
                            <div className="editor-controls">
                                {/* Edit Mode Toggle */}
                                <div className="edit-mode-toggle">
                                    <div
                                        className={`toggle-switch ${isEditMode ? 'active' : ''}`}
                                        onClick={() => setIsEditMode(!isEditMode)}
                                    >
                                        <div className="toggle-slider"></div>
                                    </div>
                                    <span className="edit-mode-label">Edit Mode</span>
                                </div>

                                <button
                                    className="sidebar-button save-btn"
                                    onClick={handleSaveDirectHtmlChanges}
                                    disabled={isGenerating || isUpdating}
                                    style={{
                                        opacity: (isGenerating || isUpdating) ? 0.6 : 1,
                                        cursor: (isGenerating || isUpdating) ? 'not-allowed' : 'pointer',
                                    }}
                                >
                                    {isUpdating ? "Saving..." : "Save"}
                                </button>

                                <svg
                                    className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
                                    onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                                    width="20"
                                    height="20"
                                    viewBox="0 0 16 16"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    style={{ cursor: 'pointer', marginLeft: '8px' }}
                                >
                                    <path fillRule="evenodd" clipRule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="#666" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Content Area */}
                    <div className="ai-comparison-page-content comparison-view">
                        <div className="comparison-result-section-new">
                            <div className="comparison-preview-main">
                                <div className="ai-preview-container">
                                    {isGenerating && !htmlContent ? (
                                        <div className="loading-container" style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            height: '88vh',
                                            backgroundColor: '#f8f9fa',
                                            padding: '40px',
                                            border: '1px solid #d1d5db',
                                            borderRadius: '8px',
                                            width: '100%'
                                        }}>
                                            <Player
                                                autoplay
                                                loop
                                                src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
                                                style={{ height: '300px', width: '300px' }}
                                            />
                                            <h1 style={{ color: '#666', marginBottom: '10px', fontWeight: 'bolder' }}>
                                                An Amazing Comparison Page is being cooked for your site!
                                            </h1>
                                            <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                                                Creating your custom comparison page
                                                This may take a few moments.
                                            </p>
                                        </div>
                                    ) : (
                                        <div className="iframe-container">
                                            <div
                                                style={{
                                                    backgroundColor: '#f3f4f6',
                                                    padding: '8px 12px',
                                                    borderBottom: '1px solid #d1d5db',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '8px',
                                                    minHeight: '40px',
                                                    position: 'sticky',
                                                    top: 0,
                                                    zIndex: 10,
                                                    flexShrink: 0
                                                }}
                                            >
                                                <div style={{ display: 'flex', gap: '6px' }}>
                                                    <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ff5f57' }} />
                                                    <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#ffbd2e' }} />
                                                    <div style={{ width: '12px', height: '12px', borderRadius: '50%', backgroundColor: '#28ca42' }} />
                                                </div>

                                                <div
                                                    style={{
                                                        flex: 1,
                                                        backgroundColor: 'white',
                                                        border: '1px solid #d1d5db',
                                                        borderRadius: '4px',
                                                        padding: '6px 12px',
                                                        fontSize: '14px',
                                                        color: '#6b7280',
                                                        marginLeft: '8px'
                                                    }}
                                                >
                                                    https://<span>{active_website_domain}</span>/<span>{comp_id?.replace(/-[a-f0-9]+$/, '') || 'comparison'}</span>
                                                </div>
                                            </div>
                                            <div
                                                style={{
                                                    backgroundColor: '#e5e7eb',
                                                    padding: '20px 16px',
                                                    borderBottom: '1px solid #d1d5db',
                                                    fontSize: '14px',
                                                    color: '#374151',
                                                    textAlign: 'center',
                                                    minHeight: '60px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    position: 'sticky',
                                                    top: '40px',
                                                    zIndex: 9,
                                                    flexShrink: 0
                                                }}
                                            >
                                                Your Existing Website Navbar
                                            </div>

                                            {/* Iframe Content */}
                                            <iframe
                                                ref={iframeRef}
                                                srcDoc={htmlContent}
                                                spellCheck="false"
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    border: 'none',
                                                    backgroundColor: 'white',
                                                    display: 'block',
                                                    flex: 1
                                                }}
                                                title="Comparison Page Preview"
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className={`comparison-sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
                                {/* Update Comparison Dropdown */}
                                <div className="sidebar-section">
                                    <div
                                        className={`sidebar-dropdown-header version-header ${updateTabOpen ? "active" : ""}`}
                                        onClick={() => setUpdateTabOpen(!updateTabOpen)}
                                    >
                                        <span><h6>What changes do you want in the Comparison Page?</h6></span>
                                    </div>

                                    {updateTabOpen && (
                                        <div className="sidebar-dropdown-content">


                                            <textarea
                                                className="sidebar-textarea"
                                                placeholder="Describe what changes you want to make..."
                                                value={userInput}
                                                onChange={(e) => setUserInput(e.target.value)}
                                                disabled={isUpdating}
                                            />
                                            <button
                                                className="sidebar-button update-btn"
                                                onClick={handleUpdateComparisonPage}
                                                disabled={isUpdating || !userInput.trim() || modifyPageMutation.isLoading}
                                                style={{
                                                    opacity: (isUpdating || !userInput.trim() || modifyPageMutation.isLoading) ? 0.6 : 1,
                                                    cursor: (isUpdating || !userInput.trim() || modifyPageMutation.isLoading) ? 'not-allowed' : 'pointer',
                                                    position: 'relative'
                                                }}
                                            >
                                                {(isUpdating || modifyPageMutation.isLoading) ? (
                                                    <span className="button-content">
                                                        <span className="spinner"></span>
                                                        {modifyPageMutation.isLoading ? 'Starting...' : 'Updating...'}
                                                    </span>
                                                ) : (
                                                    "Update Comparison Page"
                                                )}
                                            </button>
                                        </div>
                                    )}
                                </div>

                                {/* Version History Dropdown */}
                                <div className="sidebar-section">
                                    <div
                                        className={`sidebar-dropdown-header version-header ${versionsTabOpen ? "active" : ""}`}
                                        onClick={() => setVersionsTabOpen(!versionsTabOpen)}
                                    >
                                        <span><h6>Version History</h6></span>
                                        <span className="version-count">
                                            {comparisonData && versions.length === 0 ? 1 : versions.length}
                                        </span>
                                    </div>

                                    {versionsTabOpen && (
                                        <>
                                            <div className="sidebar-dropdown-content">
                                                {comparisonData && versions.length === 0 ? (
                                                    <div className="versions-list-sidebar">
                                                        <div className="version-item-sidebar current-version">
                                                            <div className="version-header-sidebar">
                                                                <div className="version-info-sidebar">
                                                                    <div className="version-number-sidebar">
                                                                        <span>Original</span>
                                                                    </div>
                                                                </div>
                                                                <div className="version-actions-sidebar" style={{
                                                                    display: 'flex',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    gap: '8px'
                                                                }}>
                                                                    <button className="sidebar-button small switch-btn" style={{ backgroundColor: '#10b981' }}>
                                                                        Current
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div className="version-description-sidebar">
                                                                Initial comparison version
                                                            </div>
                                                        </div>
                                                    </div>
                                                ) : versions.length === 0 ? (
                                                    <p className="empty-versions">
                                                        No versions available for this comparison.
                                                    </p>
                                                ) : (
                                                    <div className="versions-list-sidebar">
                                                        {versions.map((version, index) => (
                                                            <div
                                                                key={version.id}
                                                                className={`version-item-sidebar ${currentVersionId === version.id ? 'current-version' : ''}`}
                                                            >
                                                                <div className="version-header-sidebar">
                                                                    <div className="version-info-sidebar">
                                                                        <div className="version-number-sidebar">
                                                                            <span>
                                                                                {index === versions.length - 1
                                                                                    ? "Original"
                                                                                    : `v${versions.length - index}`
                                                                                }
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="version-actions-sidebar" style={{
                                                                        display: 'flex',
                                                                        justifyContent: 'center',
                                                                        alignItems: 'center',
                                                                        gap: '8px'
                                                                    }}>
                                                                        {currentVersionId === version.id ? (
                                                                            <button className="sidebar-button small switch-btn" style={{ backgroundColor: '#10b981' }}>
                                                                                Current
                                                                            </button>
                                                                        ) : (
                                                                            <>

                                                                                <button
                                                                                    className="sidebar-button small switch-btn"
                                                                                    onClick={() => switchToVersion(version.id)}
                                                                                >
                                                                                    Switch
                                                                                </button>
                                                                                {versions.length > 1 && index !== versions.length - 1 && (
                                                                                    <button
                                                                                        className="sidebar-button small danger"
                                                                                        onClick={() => deleteVersion(version.id)}
                                                                                    >
                                                                                        Delete
                                                                                    </button>
                                                                                )}
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                                <div className="version-description-sidebar">
                                                                    {version.changes_summary || 'No description available'}
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>

                                            {/* Delete Confirmation Modal */}
                                            <AbunModal
                                                active={showDeleteModal}
                                                headerText="Confirm Delete"
                                                closeable={true}
                                                closeableKey={true}
                                                closeOnOutsideClick={false}
                                                hideModal={handleCancelDelete}
                                            >
                                                <div>
                                                    <p>Are you sure you want to delete this version? This action cannot be undone.</p>
                                                    <div style={{
                                                        display: 'flex',
                                                        justifyContent: 'flex-end',
                                                        gap: '10px',
                                                        marginTop: '20px'
                                                    }}>
                                                        <button
                                                            className="button"
                                                            onClick={handleCancelDelete}
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            className="button is-danger"
                                                            onClick={handleConfirmDelete}
                                                        >
                                                            OK
                                                        </button>
                                                    </div>
                                                </div>
                                            </AbunModal>
                                        </>
                                    )}
                                </div>

                                {/* Get Embed Code Dropdown */}
                                {!isGenerating && !isUpdating && comparisonData && comparisonData.comp_id && (
                                    <div className="sidebar-section">
                                        <div
                                            className={`sidebar-dropdown-header version-header ${embedTabOpen ? "active" : ""}`}
                                            onClick={() => {
                                                setEmbedTabOpen(!embedTabOpen);
                                            }}
                                        >
                                            <span><h6>Get Embed Code</h6></span>
                                        </div>


                                        <div className="sidebar-dropdown-content">
                                            <div>
                                                <div style={{ marginBottom: '15px' }}>
                                                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                                        Add this Script Tag to your head:
                                                    </label>
                                                    <textarea
                                                        className="sidebar-textarea embed-code"
                                                        readOnly
                                                        value={comparisonScriptTag}
                                                        style={{ minHeight: '90px', fontSize: '12px' }}
                                                    />
                                                    <button
                                                        className="sidebar-button copy-btn"
                                                        onClick={() => copyToClipboard(comparisonScriptTag)}
                                                        disabled={!comparisonScriptTag}
                                                    >
                                                        Copy
                                                    </button>
                                                </div>

                                                <div style={{ marginBottom: '15px' }}>
                                                    <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                                        Add this Div Tag in the body where you want to load:
                                                    </label>
                                                    <textarea
                                                        className="sidebar-textarea embed-code"
                                                        readOnly
                                                        value={comparisonDivTag}
                                                        style={{ minHeight: '110px', fontSize: '12px' }}
                                                    />
                                                    <button
                                                        className="sidebar-button copy-btn"
                                                        onClick={() => copyToClipboard(comparisonDivTag)}
                                                        disabled={!comparisonDivTag}
                                                    >
                                                        Copy
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                )}

                                {/* Verify Script Section */}
                                {!isGenerating && !isUpdating && comparisonData && comparisonData.comp_id && (
                                    <div className="sidebar-section">
                                        <div
                                            className={`sidebar-dropdown-header version-header ${verifyTabOpen ? "active" : ""}`}
                                            onClick={() => setVerifyTabOpen(!verifyTabOpen)}
                                        >
                                            <span><h6>Verify Script Installation</h6></span>
                                        </div>

                                        {verifyTabOpen && (
                                            <div className="sidebar-dropdown-content">
                                                <div>
                                                    <div style={{ marginBottom: '15px' }}>
                                                        <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                                            Verify that the script is properly installed on your website:
                                                        </label>
                                                        <AbunButton
                                                            className="sidebar-button copy-btn"
                                                            style={{
                                                                background: isVerified ? '#10B981' : '#007bff',
                                                                borderColor: isVerified ? '#10B981' : '#007bff',
                                                                width: '100%'
                                                            }}
                                                            type="success"
                                                            clickHandler={handleVerifyScript}
                                                            disabled={isVerifying || isVerified}
                                                        >
                                                            {isVerified ? 'Verified' : isVerifying ? 'Verifying...' : 'Verify'}
                                                        </AbunButton>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <ErrorAlert ref={errorAlertRef} />
            <SuccessAlert ref={successAlertRef} />
        </>
    );
}

export default withAdminAndProductionCheck(AIComparisonPage);