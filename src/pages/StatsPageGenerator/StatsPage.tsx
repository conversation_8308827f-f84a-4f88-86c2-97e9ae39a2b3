
import { Player } from "@lottiefiles/react-lottie-player";
import { useMutation, useQuery } from '@tanstack/react-query';
import "aieditor/dist/style.css";
import { useCallback, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams, useRouteLoaderData } from "react-router-dom";
import { Tooltip } from 'react-tooltip';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import GenericButton from '../../components/GenericButton/GenericButton';
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { BasePageData } from "../Base/Base";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import {
  deleteStatsPageVersionMutation,
  generateAIStatsPageMutation,
  generateStatIdeasMutation,
  getAIStatsPageDataQuery,
  modifyAIStatsPageMutation,
  setCurrentStatsVersionFn,
  verifyToolsLoadingScriptMutation,
  checkJobStatus,
} from "../../utils/api";
import { pageURL } from "../routes";
import './StatsPageGenerator.min.css';
import StatsPageTable from "./StatsPageTable";

interface StatsDataType {
  stats_id: string;
  html_content: string;
  stats_type: string;
  stats_topic: string;
  stats_description: string;
  current_version_id?: number;
  is_processing: boolean;
  is_completed: boolean;
  is_failed: boolean;
}

interface StatsPageApiResponse {
  data: {
    data: StatsDataType & {
      versions?: StatsVersion[];
    };
  };
  status?: string;
  message?: string;
}



interface LocationState {
  statsId?: string;
  statsType?: string;
  statsTopic?: string;
  userModifications?: string[];
  fromOtherTopic?: boolean;
  fromExisting?: boolean;
  customTitle?: string;
  keyword?: string;
  fromCustomTitle?: boolean;
  navigationTimestamp?: number;
}

interface StatsVersion {
  id: number;
  version_name: string;
  html_code: string;
  changes_summary?: string;
  created_on: string;
  created_on_relative: string;
  code_length: number;
  code_preview: string;
}


declare global {
  interface Window {
    AbunStatsWidget?: {
      [statsId: string]: {
        refresh: () => void;
      };
    };
  }
}


const StatisticPage = () => {


  const basePageData = useRouteLoaderData("base") as BasePageData;

  const { active_website_domain } = basePageData;
  const { currentPlanName } = basePageData;

  // --------------------------- HOOKS ---------------------------
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;
  const iframeRef = useRef<HTMLIFrameElement>(null);


  // --------------------------- STATES ---------------------------
  const [keyword, setKeyword] = useState('');
  const [idea, setIdea] = useState('');
  const [ideas, setIdeas] = useState<string[]>([]);
  const [fullIdeas, setFullIdeas] = useState<string[]>([]);
  const [showStatisticsPage, setShowStatisticsPage] = useState(false);
  const [statsData, setStatsData] = useState<StatsDataType | null>(null);
  const [updateTabOpen, setUpdateTabOpen] = useState(true);
  const [versionsTabOpen, setVersionsTabOpen] = useState(false);
  const [embedTabOpen, setEmbedTabOpen] = useState(false);
  const [verifyTabOpen, setVerifyTabOpen] = useState(false);
  const [userInput, setUserInput] = useState<string>("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [versionToDelete, setVersionToDelete] = useState<number | null>(null);
  const isUpdatingRef = useRef(false);
  const [statsId, setStatsId] = useState<string>(state?.statsId || '');
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [versions, setVersions] = useState<StatsVersion[]>([]);
  const [currentVersionId, setCurrentVersionId] = useState<number | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [statsScriptTag, setStatsScriptTag] = useState("");
  const [statsDivTag, setStatsDivTag] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const { taskId } = useParams<{ taskId?: string }>();
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(taskId || null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState("ai-statistic");

  // --------------------------- REFS ---------------------------
  const errorAlertRef = useRef<any>(null);
  const successAlertRef = useRef<any>(null);

  // --------------------------- MUTATIONS ---------------------------
  const generateStatIdeasMut = useMutation(generateStatIdeasMutation);
  const generateAIStatsPageMut = useMutation(generateAIStatsPageMutation);


  // --------------------------- QUERIES ---------------------------
  // Query for fetching stats page data based on URL parameter
  const {
    data: statsPageData,
    isLoading: isLoadingStatsPage,
    error: statsPageError
  } = useQuery({
    ...getAIStatsPageDataQuery(statsId || ''),
    enabled: !!(statsId && statsId.trim() !== ''),
  });

  // Job status polling query
  useQuery({
    ...checkJobStatus(currentTaskId || ''),
    enabled: !!currentTaskId && (isGenerating || isUpdating),
    refetchInterval: 3000, // Poll every 3 seconds
    onSuccess: (response: any) => {
      if (response?.data?.status === "completed") {
        // Job completed successfully
        const wasUpdating = isUpdating;

        // Clear states
        setIsGenerating(false);
        setIsUpdating(false);
        setCurrentTaskId(null);

        if (wasUpdating) {
          successAlertRef.current?.show("Statistics page updated successfully!");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 3000);
        } else {
          successAlertRef.current?.show("Statistics page generated successfully!");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 3000);
        }

        // Refresh the page data if needed
        window.location.reload();
      } else if (response?.data?.status === "failed") {
        // Job failed - redirect to table page
        setIsGenerating(false);
        setIsUpdating(false);
        setCurrentTaskId(null);

        errorAlertRef.current?.show("Task failed. Redirecting to stats pages list...");
        setTimeout(() => {
          errorAlertRef.current?.close();
          navigate(pageURL.staticPageGenerator);
        }, 2000);
      }
    },
    onError: () => {
      // Handle query error
      setIsGenerating(false);
      setIsUpdating(false);
      setCurrentTaskId(null);
      errorAlertRef.current?.show("Failed to check job status. Please refresh the page.");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
    }
  });



  const modifyAIStatsPageMut = useMutation({
    ...modifyAIStatsPageMutation
  });

  // Mutation for verifying script
  const verifyScriptMutation = useMutation(verifyToolsLoadingScriptMutation);

  // Determine if any mutation is loading
  // Fix: Remove isLoadingStatsPage from isLoading calculation for input field
  const isLoading = generateStatIdeasMut.isLoading || generateAIStatsPageMut.isLoading || isGenerating;

  // --------------------------- HANDLERS ---------------------------
  const handleGenerateIdea = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!keyword.trim()) {
      errorAlertRef.current?.show("Please enter a keyword");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    generateStatIdeasMut.mutate(
      { keyword: keyword.trim() },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.ideas) {
            const fullIdeasData = data.ideas;

            setFullIdeas(fullIdeasData);

            const cleanTitle = (title) => {
              return title
                .replace(/^\d+\.\s*/, '')
                .replace(/^\*+\s*/, '')
                .replace(/\s*\*+$/, '')
                .replace(/\*+/g, '')
                .trim();
            };

            const cleanedTitles = fullIdeasData.map(idea =>
              cleanTitle(typeof idea === 'object' ? idea.title : idea)
            );

            setIdeas(cleanedTitles);

          } else {
            errorAlertRef.current?.show("No ideas received from server");
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error) => {
          errorAlertRef.current?.show("Failed to generate ideas. Please try again.");
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 3000);
        }
      }
    );
  };

  const handleIdeaClick = (selectedTitle: string) => {
    setIdea(selectedTitle);
  };

  const handleGenerateStatisticPage = useCallback(() => {

    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!idea.trim()) {
      errorAlertRef.current?.show("Please enter a statistics page idea");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    const selectedIdeaIndex = ideas.indexOf(idea.trim());

    let extractedDescription = '';
    if (fullIdeas && Array.isArray(fullIdeas) && fullIdeas.length > 0 &&
      selectedIdeaIndex >= 0 && selectedIdeaIndex < fullIdeas.length) {

      const selectedFullIdea = fullIdeas[selectedIdeaIndex];

      if (selectedFullIdea &&
        typeof selectedFullIdea === 'object' &&
        selectedFullIdea !== null &&
        'description' in selectedFullIdea &&
        typeof (selectedFullIdea as any).description === 'string') {
        extractedDescription = (selectedFullIdea as any).description.trim();
      }
    }

    generateAIStatsPageMut.mutate(
      {
        stats_topic: idea.trim(),
        stats_description: extractedDescription,
        original_keyword: keyword || '',
        selected_idea: selectedIdeaIndex >= 0 ? fullIdeas[selectedIdeaIndex] : {}
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data.status === 'rejected' && data.reason === 'max_limit_reached') {
            errorAlertRef.current?.show(
              `You have reached your maximum limit of ${data.current_usage}/${data.max_allowed} stats pages for this plan. Please upgrade!`
            );
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
            return;
          }

          if (data && (data.status === 'success' || data.status === 'processing')) {
            if (data.task_id && data.stats_id) {
              setCurrentTaskId(data.task_id);



              const tempStatsData = {
                stats_id: data.stats_id,
                html_content: '', // Will be populated after task completion
                stats_type: data.stats_type || 'generating',
                stats_topic: data.stats_topic || idea.trim(),
                stats_description: data.stats_description || extractedDescription || '',
                is_processing: true,
                is_completed: false,
                is_failed: false
              };

              setStatsId(data.stats_id);
              setStatsData(tempStatsData);
              setShowStatisticsPage(true);
              setIsGenerating(true);

              const newURL = `${pageURL['staticPageGenerator']}/${data.stats_id}`;
              navigate(newURL, { replace: true });

              successAlertRef.current?.show("Statistics page generation started...");
              setTimeout(() => {
                successAlertRef.current?.close();
              }, 3000);
            } else {
              console.error('Missing task_id or stats_id in response:', data);
              errorAlertRef.current?.show("Server response missing required data");
              setTimeout(() => {
                errorAlertRef.current?.close();
              }, 3000);
            }
          } else {
            errorAlertRef.current?.show("Unexpected response from server");
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error: any) => {
          setIsGenerating(false);
          setCurrentTaskId(null);
          setShowStatisticsPage(false);

          navigate(pageURL.staticPageGenerator, { replace: true });

          let errorMessage = "Failed to generate statistics page. Please try again.";
          if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }
          errorAlertRef.current?.show(errorMessage);
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 3000);
        }
      }
    );
  }, [idea, keyword, ideas, fullIdeas, generateAIStatsPageMut, navigate]);

  const handleUpdateStatsPage = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!userInput.trim() || !statsId) {
      errorAlertRef.current?.show("Please enter modifications and ensure stats page exists");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    setIsUpdating(true);

    let currentHtmlContent = htmlContent;

    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        currentHtmlContent = doc.documentElement.outerHTML;
        // Update state with current iframe content
        setHtmlContent(currentHtmlContent);
      }
    }

    modifyAIStatsPageMut.mutate(
      {
        stats_id: statsId,
        modifications: userInput.trim(),
        html_content: currentHtmlContent
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.status === 'processing' && data.task_id) {
            // Set the task ID for job status tracking
            setCurrentTaskId(data.task_id);



            successAlertRef.current?.show("Statistics page modification started. Please wait...");
            setTimeout(() => {
              successAlertRef.current?.close();
            }, 3000);
          } else {
            setIsUpdating(false);
            errorAlertRef.current?.show("Failed to start modification task");
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error: any) => {
          setIsUpdating(false);

          let errorMessage = "Failed to update statistics page.";

          if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
            errorMessage = "Request timed out. The server might still be processing. Please wait a moment and check if the update was applied.";
          } else if (error.message?.includes('broken pipe')) {
            errorMessage = "Connection was interrupted. Please try again.";
          } else if (error.response?.status === 404) {
            errorMessage = "API endpoint not found. Make sure DEBUG mode is enabled.";
          } else if (error.response?.data?.err_id === 'NO_STATS_PAGE_FOUND') {
            errorMessage = "Statistics page not found. Please generate a new one.";
          } else if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          }

          errorAlertRef.current?.show(errorMessage);
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 3000);
        }
      }
    );
  };

  const handleSaveDirectHtmlChanges = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!statsId) {
      errorAlertRef.current?.show("Stats page not found");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    let currentHtmlContent = htmlContent;

    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        currentHtmlContent = doc.documentElement.outerHTML;
      }
    }

    if (!currentHtmlContent.trim()) {
      errorAlertRef.current?.show("No content to save");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    modifyAIStatsPageMut.mutate(
      {
        stats_id: statsId,
        modifications: "Direct HTML content update",
        html_content: currentHtmlContent.trim()
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.status === 'processing' && data.task_id) {
            // Set the task ID for job status tracking
            setCurrentTaskId(data.task_id);



            setIsUpdating(true);
            successAlertRef.current?.show("Saving changes. Please wait...");
            setTimeout(() => {
              successAlertRef.current?.close();
            }, 3000);
          } else {
            setIsUpdating(false);
            errorAlertRef.current?.show("Failed to start modification task");
            setTimeout(() => {
              errorAlertRef.current?.close();
            }, 3000);
          }
        },
        onError: (error) => {
          setIsUpdating(false);
          errorAlertRef.current?.show("Failed to save changes");
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 3000);
        }
      }
    );
  };

  const lastKnownContent = useRef('');

  useEffect(() => {
    if (iframeRef.current && htmlContent && !isUpdatingRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;

      if (doc) {
        const currentDocContent = doc.documentElement.outerHTML;

        if (currentDocContent !== htmlContent && lastKnownContent.current !== htmlContent) {
          doc.open();
          doc.write(htmlContent);
          doc.close();
          lastKnownContent.current = htmlContent;
        }

        if (doc.body && !isUpdating && !modifyAIStatsPageMut.isLoading) {
          doc.body.contentEditable = 'true';

          const existingListener = doc.body.getAttribute('data-listener-added');
          if (!existingListener) {
            doc.body.setAttribute('data-listener-added', 'true');

            let updateTimeout;
            doc.body.addEventListener('input', (e) => {
              clearTimeout(updateTimeout);

              updateTimeout = setTimeout(() => {
                if (!isUpdatingRef.current) {
                  isUpdatingRef.current = true;
                  const newContent = doc.documentElement.outerHTML;

                  if (newContent !== lastKnownContent.current) {
                    setHtmlContent(newContent);
                    lastKnownContent.current = newContent;
                  }

                  setTimeout(() => {
                    isUpdatingRef.current = false;
                  }, 100);
                }
              }, 500);
            });
          }
        } else if (doc.body) {
          doc.body.contentEditable = 'false';
        }
      }
    }
  }, [htmlContent, isUpdating, modifyAIStatsPageMut.isLoading]);





  useEffect(() => {
    if (taskId && !currentTaskId) {
      setCurrentTaskId(taskId);
    }
  }, [taskId, currentTaskId]);

  // Handle URL-based stats ID detection
  useEffect(() => {
    const currentPath = location.pathname;
    const isSpecificStatsPage = currentPath.includes('/ai-stats-page-generator/') && currentPath !== pageURL.staticPageGenerator;

    if (isSpecificStatsPage) {
      const urlParts = currentPath.split('/');
      const urlStatsId = urlParts[urlParts.length - 1];

      if (urlStatsId && urlStatsId !== 'ai-stats-page-generator') {
        setStatsId(urlStatsId);
        setShowStatisticsPage(true);
      }
    } else {
      setShowStatisticsPage(false);
      setStatsId('');
    }
  }, [location.pathname]);



  // Safety timeout to prevent infinite loading (5 minutes max)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if ((isGenerating || isUpdating) && currentTaskId) {
      timeoutId = setTimeout(() => {
        setIsGenerating(false);
        setIsUpdating(false);
        setCurrentTaskId(null);



        errorAlertRef.current?.show("Task is taking longer than expected. Please try again or check your statistics pages list.");
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 3000);
      }, 5 * 60 * 1000); // 5 minutes
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isGenerating, isUpdating, currentTaskId, statsId]);

  const switchToVersion = async (versionId: number) => {
    const selectedVersion = versions.find(v => v.id === versionId);
    if (!selectedVersion || !statsData) {
      return;
    }

    try {
      setCurrentVersionId(versionId);
      setHtmlContent(selectedVersion.html_code);
      lastKnownContent.current = selectedVersion.html_code; // Update ref

      const updatedStatsData: StatsDataType = {
        ...statsData,
        html_content: selectedVersion.html_code,
        current_version_id: versionId
      };
      setStatsData(updatedStatsData);

      // Sync with backend
      const response = await setCurrentStatsVersionFn(statsData.stats_id, versionId);

      if (response.status !== 200 && response.data?.status !== 'success') {
        console.error('Backend update failed:', response);
      }
    } catch (error) {
      console.error('Failed to switch version:', error);
    }
  };

  useEffect(() => {
    if (currentVersionId && versions.length > 0) {
      const savedVersion = versions.find(v => v.id === currentVersionId);
      if (savedVersion && savedVersion.html_code !== htmlContent) {
        // Only update if content is actually different
        setHtmlContent(savedVersion.html_code);
      }
    }
  }, [currentVersionId, versions]);

  // Helper function to process stats data (from API or navigation state)
  const processStatsData = (data: any) => {
    const {
      stats_id,
      html_content,
      stats_type,
      stats_topic,
      stats_description,
      versions,
      current_version_id,
      encrypted_id,
      encrypted_tool_id,
      ai_stats_page_script_verified,
      is_processing,
      is_completed,
      is_failed,
      task_id
    } = data;

    const statsDataObj: StatsDataType = {
      stats_id,
      html_content,
      stats_type,
      stats_topic,
      stats_description,
      current_version_id,
      is_processing: is_processing || false,
      is_completed: is_completed || false,
      is_failed: is_failed || false
    };

    setStatsId(stats_id);
    setStatsData(statsDataObj);

    // Check if the stats page is currently being processed
    if (is_processing) {
      // For processing state, we'll show the cooking page instead of the stats page
      // This handles both generation and modification scenarios
      setIsGenerating(true);
      setShowStatisticsPage(false); // Don't show the stats page, show cooking content instead
      // Start polling for job status if we have a task_id
      if (task_id) {
        setCurrentTaskId(task_id);
      }
    } else {
      setShowStatisticsPage(true);
    }

    // Set script tags and verification status
    if (encrypted_id && encrypted_tool_id) {
      setStatsScriptTag(`<script async src="${process.env.REACT_APP_FRONTEND_DOMAIN}/js/${getSnippetFileName()}" data-user-id="${encrypted_id}" data-tool-name="ai-stats-page"></script>`);
      setStatsDivTag(`<div data-ai-stats-page-id="${encrypted_tool_id}"></div>`);
    }

    // Set verification status
    setIsVerified(ai_stats_page_script_verified || false);

    if (versions && Array.isArray(versions) && versions.length > 0) {
      setVersions(versions);

      const versionToSet = current_version_id || versions[0].id;
      setCurrentVersionId(versionToSet);

      const initialVersion = versions.find(v => v.id === versionToSet);
      if (initialVersion) {
        setHtmlContent(initialVersion.html_code);
      } else {
        setHtmlContent(html_content);
      }
    } else {
      setVersions([]);
      setCurrentVersionId(null);
      setHtmlContent(html_content);
    }

    if (state?.fromOtherTopic) {
      successAlertRef.current?.show("New statistics page created and loaded successfully!");
      setTimeout(() => {
        successAlertRef.current?.close();
      }, 3000);
    } else if (state?.fromExisting) {
      successAlertRef.current?.show(`Statistics page loaded successfully!`);
      setTimeout(() => {
        successAlertRef.current?.close();
      }, 3000);
    }
  };

  // Handle data from API query
  useEffect(() => {
    if (statsPageData) {
      const response = statsPageData as StatsPageApiResponse;

      if (response?.data?.data) {
        const statsData = response.data.data;
        processStatsData(statsData);
      } else {
        errorAlertRef.current?.show("Invalid data structure received from server");
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 3000);
      }
    }
  }, [statsPageData]);

  // Handle API errors
  useEffect(() => {
    if (statsPageError && statsId) {
      errorAlertRef.current?.show("Failed to load stats page data. Please try again.");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
    }
  }, [statsPageError, statsId]);


  const deleteStatsPageVersionMut = useMutation({
    ...deleteStatsPageVersionMutation,
    onSuccess: (data, variables) => {
      const deletedVersionId = variables.version_id;

      setVersions(prev => prev.filter(v => v.id !== deletedVersionId));

      if (currentVersionId === deletedVersionId) {
        const remainingVersions = versions.filter(v => v.id !== deletedVersionId);
        if (remainingVersions.length > 0) {
          switchToVersion(remainingVersions[0].id);
        }
      }

      successAlertRef.current?.show("Version deleted successfully!");
      setTimeout(() => {
        successAlertRef.current?.close();
      }, 3000);
    },
    onError: (error, variables) => {

      // Handle specific error cases
      if (error?.response?.data?.err_id === 'CANNOT_DELETE_LAST_VERSION') {
        errorAlertRef.current?.show("Cannot delete the last remaining version");
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 3000);
      } else if (error?.response?.data?.err_id === 'VERSION_NOT_FOUND') {
        errorAlertRef.current?.show("Version not found or access denied");
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 3000);
      } else {
        errorAlertRef.current?.show("Failed to delete version. Please try again.");
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 3000);
      }
    }
  });

  const deleteVersion = (versionId: number) => {
    // Check if this is the last version
    if (versions.length <= 1) {
      errorAlertRef.current?.show("Cannot delete the last remaining version");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
      return;
    }

    setVersionToDelete(versionId);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (versionToDelete !== null) {
      // Trigger the mutation
      deleteStatsPageVersionMut.mutate({ version_id: versionToDelete });

      // Reset state
      setShowDeleteModal(false);
      setVersionToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setVersionToDelete(null);
  };

  // Helper function to get snippet filename based on environment
  const getSnippetFileName = () => {
    if (process.env.REACT_APP_DRF_DOMAIN === 'https://api.abun.com') {
      return 'ai-stats-snippet.js';
    } else if (process.env.REACT_APP_DRF_DOMAIN === 'https://staging.api.abun.com') {
      return 'ai-stats-snippet-staging.js';
    } else {
      return 'ai-stats-snippet-dev.js';
    }
  };

  // Verification handler
  const handleVerifyScript = () => {
    setIsVerifying(true);

    verifyScriptMutation.mutate({ toolName: "ai-stats-page" }, {
      onSuccess: (response: any) => {
        setIsVerifying(false);
        const responseData = response.data;

        if (responseData.success) {
          setIsVerified(true);
          successAlertRef.current?.show("Script verification successful! Your website is properly configured.");
          setTimeout(() => {
            successAlertRef.current?.close();
          }, 5000);
        } else {
          // Handle different error types based on err_id
          let errorMessage = responseData.message || "Verification failed";

          switch (responseData.err_id) {
            case "NO_WEBSITE_FOUND":
              errorMessage = "No website found. Please ensure your website is properly connected.";
              break;
            case "WEBSITE_NOT_ACCESSIBLE":
              errorMessage = "Website not accessible. Please check if your website is online and accessible.";
              break;
            case "SCRIPT_TAG_NOT_FOUND":
              errorMessage = "Script tag not found on your website. Please ensure the script is properly installed.";
              break;
            case "INVALID_USER_EMAIL":
              errorMessage = "Invalid user email. Please contact support if this issue persists.";
              break;
            default:
              errorMessage = responseData.message || "Verification failed. Please try again.";
          }

          errorAlertRef.current?.show(errorMessage);
          setTimeout(() => {
            errorAlertRef.current?.close();
          }, 5000);
        }
      },
      onError: (error: any) => {
        setIsVerifying(false);
        console.error('Error verifying script:', error);

        const errorMessage = error?.response?.data?.message || "Failed to verify script. Please try again.";
        errorAlertRef.current?.show(errorMessage);
        setTimeout(() => {
          errorAlertRef.current?.close();
        }, 5000);
      }
    });
  };

  // To copy Script and div tag
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      successAlertRef.current?.show("Copied to clipboard!");
      setTimeout(() => {
        successAlertRef.current?.close();
      }, 3000);
    }).catch(err => {
      errorAlertRef.current?.show("Failed to copy to clipboard.");
      setTimeout(() => {
        errorAlertRef.current?.close();
      }, 3000);
    });
  };

  // For responsive behavior - auto-collapse on small screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle URL changes and browser navigation
  useEffect(() => {
    const currentPath = location.pathname;
    const isMainStatsPage = currentPath === pageURL.staticPageGenerator;

    // Only reset if we're on the main stats page AND we're currently showing a stats page
    // AND we're not in the middle of generating or updating (to avoid interfering with normal flow)
    if (isMainStatsPage && showStatisticsPage && !isGenerating && !isUpdating && !currentTaskId) {
      setShowStatisticsPage(false);
      setStatsData(null);
      setStatsId('');
      setHtmlContent('');
      setVersions([]);
      setCurrentVersionId(null);
      setUserInput('');


    }
  }, [location.pathname, showStatisticsPage, isGenerating, isUpdating, currentTaskId, statsId]);

  // Navigate back from stats page
  const backToList = () => {
    // Reset all states before navigation
    setShowStatisticsPage(false);
    setStatsData(null);
    setStatsId('');
    setHtmlContent('');
    setVersions([]);
    setCurrentVersionId(null);
    setUserInput('');
    setIsGenerating(false);
    setIsUpdating(false);
    setCurrentTaskId(null);



    // Navigate to the main page
    navigate(pageURL.staticPageGenerator, { replace: true });
  };

  // --------------------------- RENDER ---------------------------
  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div
        className={`statistic-page-generator-container ${showStatisticsPage ? 'full-stats-view' : ''}`}
      >
        {showStatisticsPage && (
          <div className={`card statistic-page-generator-header w-100 ${showStatisticsPage ? 'stats-view' : ''}`}>

            <div className="left-header-section">
              <span
                className="back-btn"
                onClick={backToList}
              >
                <svg
                  className="back-btn"
                  width="30"
                  height="24"
                  viewBox="0 0 30 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                </svg>
              </span>

              <a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
                <svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48" >
                  <rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" stroke-width="3" />
                  <rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
                  <path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
                </svg>
              </a>

              <div className="Tabs">
                <div className="Tab active">
                  Stats Page
                </div>
              </div>
            </div>

            <div className="right-header-section">
              <div className="editor-controls">
                <button
                  className="sidebar-button save-btn"
                  onClick={handleSaveDirectHtmlChanges}
                  disabled={isGenerating || modifyAIStatsPageMut.isLoading || isUpdating}
                  style={{
                    opacity: (modifyAIStatsPageMut.isLoading || isUpdating) ? 0.6 : 1,
                    cursor: (modifyAIStatsPageMut.isLoading || isUpdating) ? 'not-allowed' : 'pointer'
                  }}
                >
                  {modifyAIStatsPageMut.isLoading ? "Saving..." : "Save"}
                </button>

                <svg
                  className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
                  onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                  width="20"
                  height="20"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ cursor: 'pointer', marginLeft: '8px' }}
                >
                  <path fillRule="evenodd" clipRule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="#666" />
                </svg>
              </div>
            </div>

          </div>
        )}

        <div className={`statistic-page-generator-content ${showStatisticsPage ? 'stats-view' : 'generator-view'}`}>
          {!showStatisticsPage && (
            <>
              <div className="is-flex is-justify-content-space-between">
                <div className="mb-5 statistic-generator-header">
                  <h2>Statistic Page Generator</h2>
                  <p>Create SEO-Optimized Stat Pages That Rank and Retain</p>
                </div>
                <span className="mt-2">
                </span>
              </div>

              <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                <ul>
                  <li className={activeTab === "ai-statistic" ? "is-active" : ""}>
                    <a onClick={() => setActiveTab("ai-statistic")}>AI Statistic Pages</a>
                  </li>
                  <li className={activeTab === "statistic-projects" ? "is-active" : ""}>
                    <a onClick={() => setActiveTab("statistic-projects")}>Projects</a>
                  </li>
                </ul>
              </div>
            </>
          )}

          {/* Show loading state if we're fetching stats page data */}
          {isLoadingStatsPage && statsId && !showStatisticsPage ? (
            <div className="loading-container" style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '50vh',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px',
              padding: '40px'
            }}>
              <div className="spinner" style={{
                border: '4px solid #f3f3f3',
                borderTop: '4px solid #3498db',
                borderRadius: '50%',
                width: '50px',
                height: '50px',
                animation: 'spin 1s linear infinite'
              }}></div>
              <h3 style={{ color: '#666', marginTop: '20px', fontWeight: 'bold' }}>
                Loading Statistics Page...
              </h3>
              <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                Fetching your statistics page data.
              </p>
            </div>
          ) : !showStatisticsPage ? (
            <>

              {activeTab === "ai-statistic" && (

                <div>
                  <div className="ca-content-row">
                    <div className="ca-form-column">
                      <form onSubmit={(e) => { e.preventDefault(); handleGenerateIdea(); }} style={{ marginTop: '-20px' }}>
                        <label className="ca-label">Keyword</label>
                        <input
                          className="ca-input"
                          type="text"
                          value={keyword}
                          onChange={(e) => setKeyword(e.target.value)}
                          placeholder="Enter keyword"
                          onBlur={() => {
                            if (keyword.trim()) {
                              handleGenerateIdea();
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleGenerateIdea();
                            }
                          }}
                          style={{ marginTop: '3px', textAlign: 'start' }}
                          disabled={isLoading}
                        />
                      </form>

                      <label className="ca-label">Stats Page Title</label>
                      <input
                        className="ca-input"
                        type="text"
                        value={idea}
                        placeholder="Enter Stats Page Title or choose from the widget"
                        onChange={(e) => setIdea(e.target.value)}
                        style={{ textAlign: 'start' }}
                        disabled={isLoading}
                        required
                      />

                      <div className="Generate_button_large" style={{ position: 'relative', display: 'inline-block' }}>

                        <GenericButton
                          text={isGenerating || generateAIStatsPageMut.isLoading ? 'Generating Page...' : "Generate Page ➜"}
                          type={"primary"}
                          width={"219px"}
                          height={"40px"}
                          left={"7px"}
                          outlined={true}
                          disable={isLoading || !idea.trim() || isGenerating}
                          additionalClassList={["is-small", "more-rounded-borders"]}
                          clickHandler={handleGenerateStatisticPage}
                          style={{ fontSize: "1rem", borderRadius: "5px" }}
                        />

                      </div>



                    </div>

                    <div className="ca-suggestion-box">
                      <h3 className="ca-suggestion-title">Statistic Page Ideas based on keyword</h3>
                      <div className="ca-suggestion-content">
                        {generateStatIdeasMut.isLoading ? (
                          <div className="ca-loading-state">
                            <div className="ca-loading-text">Generating Ideas, please wait...</div>
                          </div>
                        ) : ideas.length > 0 ? (
                          <ul className="ca-suggestion-list">
                            {ideas.map((title, idx) => (
                              <li
                                key={idx}
                                className="ca-suggestion-item"
                                data-tooltip-id="tooltip"
                                data-tooltip-content={keyword ? "Click to use Title" : "Please enter a keyword"}
                                onClick={() => {
                                  if (keyword) {
                                    handleIdeaClick(title);
                                  }
                                }}
                                style={{
                                  fontWeight: idea === title ? '500' : '600',
                                  paddingLeft: idea === title ? '13px' : '16px',
                                  padding: '1px 16px',
                                  borderRadius: '4px',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease'
                                }}
                              >
                                {title}
                              </li>
                            ))}

                            <Tooltip id="tooltip" place="bottom" />
                            <Tooltip id="textarea_btn" place="top" />
                            <Tooltip id="style_button" place="bottom" />
                          </ul>
                        ) : (
                          <div className="ca-empty-state">
                            <h3 className="ca-empty-state-title">
                              Enter Keyword to get Ideas
                            </h3>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="Generate_button">
                    <GenericButton
                      text={isGenerating || generateAIStatsPageMut.isLoading ? 'Generating Page...' : "Generate Page ➜"}
                      type={"primary"}
                      width={"219px"}
                      height={"40px"}
                      left={"7px"}
                      outlined={true}
                      disable={isLoading || !idea.trim() || isGenerating}
                      additionalClassList={["is-small", "more-rounded-borders"]}
                      clickHandler={handleGenerateStatisticPage}
                      style={{ fontSize: "1rem", borderRadius: "5px" }}
                    />
                  </div>
                </div>
              )}

              {activeTab === "statistic-projects" && (
                <StatsPageTable />
              )}


            </>
          ) : (

            <div className="stats-result-section-new">
              <div className="stats-preview-main">
                <div className="ai-preview-container">
                  {isGenerating && !htmlContent ? (
                    <div className="loading-container" style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      backgroundColor: '#f8f9fa',
                      borderRadius: '8px',
                      padding: '40px'
                    }}>
                      <Player
                        autoplay
                        loop
                        src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
                        style={{ height: '300px', width: '300px' }}
                      >
                      </Player>
                      <h1 style={{ color: '#666', marginBottom: '10px', fontWeight: 'bolder' }}>
                        An Amazing Stats Page is being cooked for your site!
                      </h1>
                      <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                        Creating your custom statistics page for "{statsData?.stats_topic}".
                        This may take a few moments.
                      </p>
                    </div>
                  ) : (
                    <div
                      style={{
                        border: '1px solid #d1d5db',
                        borderRadius: '8px',
                        backgroundColor: 'white',
                        width: '100%',
                        height: '88vh',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <div
                        style={{
                          backgroundColor: '#f3f4f6',
                          padding: '8px 12px',
                          borderBottom: '1px solid #d1d5db',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          minHeight: '40px',
                          position: 'sticky',
                          top: 0,
                          zIndex: 10,
                          flexShrink: 0
                        }}
                      >
                        <div style={{ display: 'flex', gap: '6px' }}>
                          <div
                            style={{
                              width: '12px',
                              height: '12px',
                              borderRadius: '50%',
                              backgroundColor: '#ff5f57'
                            }}
                          />
                          <div
                            style={{
                              width: '12px',
                              height: '12px',
                              borderRadius: '50%',
                              backgroundColor: '#ffbd2e'
                            }}
                          />
                          <div
                            style={{
                              width: '12px',
                              height: '12px',
                              borderRadius: '50%',
                              backgroundColor: '#28ca42'
                            }}
                          />
                        </div>

                        {/* URL Bar */}
                        <div
                          style={{
                            flex: 1,
                            backgroundColor: 'white',
                            border: '1px solid #d1d5db',
                            borderRadius: '4px',
                            padding: '6px 12px',
                            fontSize: '14px',
                            color: '#6b7280',
                            marginLeft: '8px'
                          }}
                        >
                          https://<span>{active_website_domain}</span>/<span>{statsId.replace(/-[a-f0-9]+$/, '')}</span>
                        </div>
                      </div>

                      {/* Navbar - Sticky */}
                      <div
                        style={{
                          backgroundColor: '#e5e7eb',
                          padding: '20px 16px',
                          borderBottom: '1px solid #d1d5db',
                          fontSize: '14px',
                          color: '#374151',
                          textAlign: 'center',
                          minHeight: '60px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'sticky',
                          top: '40px',
                          zIndex: 9,
                          flexShrink: 0
                        }}
                      >
                        Your Existing Website Navbar
                      </div>

                      {/* Main Scrollable Content Area */}

                      {/* Iframe Content */}
                      <iframe
                        ref={iframeRef}
                        srcDoc={htmlContent}
                        style={{
                          width: '100%',
                          height: '100%',
                          border: 'none',
                          backgroundColor: 'white',
                          display: 'block',
                          flex: 1
                        }}
                        title="Statistics Page Preview"
                      />


                    </div>
                  )}
                </div>
              </div>

              <div className={`stats-sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
                {/* Update Statistics Dropdown */}
                <div className="sidebar-section">
                  <div
                    className={`sidebar-dropdown-header version-header ${updateTabOpen ? "active" : ""}`}
                    onClick={() => setUpdateTabOpen(!updateTabOpen)}
                  >
                    <span><h6>What changes do you want in the Statistics Page?</h6></span>
                  </div>

                  {updateTabOpen && (
                    <div className="sidebar-dropdown-content">
                      <textarea
                        className="sidebar-textarea"
                        placeholder="Use different colors and add this..."
                        value={userInput}
                        onChange={(e) => setUserInput(e.target.value)}
                        disabled={isUpdating}
                      />
                      <button
                        className="sidebar-button update-btn"
                        onClick={handleUpdateStatsPage}
                        disabled={isUpdating || !userInput.trim() || modifyAIStatsPageMut.isLoading || isGenerating}
                        style={{
                          opacity: (isUpdating || modifyAIStatsPageMut.isLoading || isGenerating) ? 0.6 : 1,
                          cursor: (isUpdating || modifyAIStatsPageMut.isLoading || isGenerating) ? 'not-allowed' : 'pointer',
                          position: 'relative'
                        }}
                      >
                        {isUpdating ? (
                          <span className="button-content">
                            <span className="spinner"></span>
                            Updating...
                          </span>
                        ) : (
                          "Update Statistics Page"
                        )}
                      </button>
                    </div>
                  )}
                </div>

                {/* Version History Dropdown */}
                <div className="sidebar-section">
                  <div
                    className={`sidebar-dropdown-header version-header ${versionsTabOpen ? "active" : ""}`}
                    onClick={() => setVersionsTabOpen(!versionsTabOpen)}
                  >
                    <span><h6>Version History</h6></span>
                    <span className="version-count">
                      {versions.length}
                    </span>
                  </div>
                  {versionsTabOpen && (
                    <>
                      <div className="sidebar-dropdown-content">
                        {versions.length === 0 ? (
                          <p className="empty-versions">
                            No versions available for this page.
                          </p>
                        ) : (
                          <div className="versions-list-sidebar">
                            {versions.map((version, index) => (
                              <div
                                key={version.id}
                                className={`version-item-sidebar ${currentVersionId === version.id ? 'current-version' : ''}`}
                              >
                                <div className="version-header-sidebar">
                                  <div className="version-info-sidebar">
                                    <div className="version-number-sidebar">
                                      <span>
                                        {index === versions.length - 1
                                          ? "Original"
                                          : `v${versions.length - index}`
                                        }
                                      </span>
                                    </div>
                                  </div>
                                  <div className="version-actions-sidebar" style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    gap: '8px'
                                  }}>
                                    {currentVersionId === version.id ? (
                                      <button className="sidebar-button small switch-btn" style={{ backgroundColor: '#10b981' }}>
                                        Current
                                      </button>
                                    ) : (
                                      <>
                                        <button
                                          className="sidebar-button small switch-btn"
                                          onClick={() => switchToVersion(version.id)}
                                        >
                                          Switch
                                        </button>
                                        {/* Only show delete button if it's not the original version */}
                                        {versions.length > 1 && index !== versions.length - 1 && (
                                          <button
                                            className="sidebar-button small danger"
                                            onClick={() => deleteVersion(version.id)}
                                          >
                                            Delete
                                          </button>
                                        )}
                                      </>
                                    )}
                                  </div>
                                </div>
                                <div className="version-description-sidebar">
                                  {version.changes_summary || 'No description available'}
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Delete Confirmation Modal */}
                      <AbunModal
                        active={showDeleteModal}
                        headerText="Confirm Delete"
                        closeable={true}
                        closeableKey={true}
                        closeOnOutsideClick={false}
                        hideModal={handleCancelDelete}
                      >
                        <div>
                          <p>Are you sure you want to delete this version? This action cannot be undone.</p>
                          <div style={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: '10px',
                            marginTop: '20px'
                          }}>
                            <button
                              className="button"
                              onClick={handleCancelDelete}
                            >
                              Cancel
                            </button>
                            <button
                              className="button is-danger"
                              onClick={handleConfirmDelete}
                            >
                              OK
                            </button>
                          </div>
                        </div>
                      </AbunModal>
                    </>
                  )}

                </div>

                {/* Get Embed Code Dropdown */}
                {!isGenerating && !isUpdating && statsData && statsData.stats_id && (
                  <div className="sidebar-section">
                    <div
                      className={`sidebar-dropdown-header version-header ${embedTabOpen ? "active" : ""}`}
                      onClick={() => {
                        setEmbedTabOpen(!embedTabOpen);
                      }}
                    >
                      <span><h6>Get Embed Code</h6></span>
                    </div>

                    {embedTabOpen && (
                      <div className="sidebar-dropdown-content">
                        {htmlContent && (
                          <div>

                            <div>
                              <div style={{ marginBottom: '15px' }}>
                                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                  Add this Script Tag to your head:
                                </label>
                                <textarea
                                  className="sidebar-textarea embed-code"
                                  readOnly
                                  value={statsScriptTag}
                                  style={{ minHeight: '90px', fontSize: '12px' }}
                                />
                                <button
                                  className="sidebar-button copy-btn"
                                  onClick={() => copyToClipboard(statsScriptTag)}
                                  disabled={!statsScriptTag}
                                >
                                  Copy
                                </button>
                              </div>
                              <div style={{ marginBottom: '15px' }}>
                                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                  Add this Div Tag in the body where you want to load:
                                </label>
                                <textarea
                                  className="sidebar-textarea embed-code"
                                  readOnly
                                  value={statsDivTag || '<!-- Div tag will be generated here -->'}
                                  style={{ minHeight: '110px', fontSize: '12px' }}
                                />
                                <button
                                  className="sidebar-button copy-btn"
                                  onClick={() => copyToClipboard(statsDivTag)}
                                  disabled={!statsDivTag}
                                >
                                  Copy
                                </button>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Verify Script Section */}
                {!isGenerating && !isUpdating && statsData && statsData.stats_id && (
                  <div className="sidebar-section">
                    <div
                      className={`sidebar-dropdown-header version-header ${verifyTabOpen ? "active" : ""}`}
                      onClick={() => setVerifyTabOpen(!verifyTabOpen)}
                    >
                      <span><h6>Verify Script Installation</h6></span>
                    </div>

                    {verifyTabOpen && (
                      <div className="sidebar-dropdown-content">
                        <div>
                          <div style={{ marginBottom: '15px' }}>
                            <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                              Verify that the script is properly installed on your website:
                            </label>
                            <AbunButton
                              className="sidebar-button copy-btn"
                              style={{
                                background: isVerified ? '#10B981' : '#007bff',
                                borderColor: isVerified ? '#10B981' : '#007bff',
                                width: '100%'
                              }}
                              type="success"
                              clickHandler={handleVerifyScript}
                              disabled={isVerifying || isVerified}
                            >
                              {isVerified ? 'Verified' : isVerifying ? 'Verifying...' : 'Verify'}
                            </AbunButton>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <ErrorAlert ref={errorAlertRef} />
        <SuccessAlert ref={successAlertRef} />
      </div>
    </>
  );
};

export default withAdminAndProductionCheck(StatisticPage);